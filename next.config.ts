import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  webpack: (config) => {
    // Exclude docs directory from webpack compilation
    // Modify existing rules to exclude docs directory
    config.module.rules.forEach((rule: any) => {
      if (rule.test && rule.test.toString().includes('tsx?')) {
        rule.exclude = rule.exclude
          ? [rule.exclude, /docs\//]
          : /docs\//;
      }
    });
    return config;
  },
};

export default nextConfig;
