# PRDGeneral MVP: 7-Day LLM Wrapper

## Executive Summary

PRDGeneral MVP is a **streamlined web app** that wraps <PERSON> to convert product ideas into PRDs through relentless clarification. Built with Next.js 15.3 + Turbopack for rapid development, it delivers the exact experience you validated in a shareable, hosted format.

## Objective/Goal

Ship in 7 days:
- Claude-powered clarification engine via web chat
- One-click PRD generation from conversations  
- Clean markdown export
- Public deployment on Vercel

## Core Features (7-Day Scope)

### 1. LLM Chat Interface
**Description**: Minimal chat UI powered by <PERSON> that acts as PRDGeneral.

**Implementation**:
- Next.js 15.3 with Turbopack (`next dev --turbo`)
- Vercel AI SDK 4.2+ with streaming
- Server-side API routes only (no client exposure)
- Session-based conversation memory

### 2. PRD Generation Mode
**Description**: "Generate PRD" button switches Claude from clarification to generation.

**Implementation**:
- Context switch in system prompt
- Markdown preview panel
- Real-time streaming of PRD content
- Copy/Download buttons

### 3. Landing & Start Flow
**Description**: Simple landing page → Start button → Chat interface.

**Implementation**:
- Static landing with example
- No auth (public MVP)
- Rate limiting via IP (10 PRDs/day)
- Clear value proposition

## Technical Stack (Updated for 2025)

```
Core:
- Next.js 15.3 (App Router)
- React 19 (stable in Next.js 15.1+)
- Turbopack for development
- TypeScript 5.5+

AI Integration:
- Vercel AI SDK 4.2+
- Claude 3.5 Sonnet via @ai-sdk/anthropic
- Server-side streaming

Styling:
- Tailwind CSS 3.4+
- Tailwind Typography for PRD preview

Deployment:
- Vercel (automatic from GitHub)
- Environment variables for API keys
```

## Day-by-Day Implementation

**Day 1: Project Setup & Core Chat**
- `npx create-next-app@latest PRDGeneral --turbo --typescript --tailwind --app`
- Install Vercel AI SDK: `npm install ai @ai-sdk/anthropic`
- Basic chat UI with messages array
- Claude API integration in `/app/api/chat/route.ts`

**Day 2: PRDGeneral Behavior**
- Refine system prompt for clarification personality
- Add "Generate PRD" detection and mode switching
- Implement conversation context management
- Test clarification flow with real ideas

**Day 3: PRD Generation & Preview**
- PRD generation prompt engineering
- Markdown preview component
- Split-screen layout (chat + preview)
- Download functionality

**Day 4: UI Polish & Error Handling**
- Responsive design for mobile
- Loading states and streaming indicators
- Error boundaries and fallbacks
- Rate limiting implementation

**Day 5: Landing Page & Deploy**
- Landing page with value prop
- Example PRD showcase
- Deploy to Vercel
- Environment variable setup

**Day 6: Testing & Refinement**
- End-to-end testing
- Prompt optimization based on outputs
- Performance testing
- Bug fixes

**Day 7: Launch Prep**
- Final polish
- ProductHunt assets
- Social media posts
- Monitor production

## Updated System Prompt

```
You are PRDGeneral, a focused PRD clarification engine.

Your mission: Transform vague product ideas into crystal-clear, single-purpose PRDs.

Personality:
- Relentlessly clarifying - never accept vague descriptions
- Enforce "do one thing well" philosophy strictly
- Push back on feature creep immediately
- Direct, helpful, but uncompromising on clarity

Process:
1. Start with "What's your product idea?" 
2. Ask pointed follow-ups until you have:
   - ONE specific problem being solved
   - ONE core feature/action
   - Clear target user
   - Measurable success metric
3. Challenge any multi-purpose tendencies
4. Keep asking until crystal clear

When user says "Generate PRD" or similar:
- Switch to PRD generation mode
- Create a focused PRD following Solo Entrepreneur Framework
- Include: Summary, Goal, Core Feature, Requirements, Success Metrics
- Keep under 800 words
- Output in clean markdown
```

## File Structure (Next.js 15)

```
/app
  /api
    /chat
      route.ts          # Claude streaming endpoint
  page.tsx              # Landing page
  /chat
    page.tsx            # Chat interface
    loading.tsx         # Loading state
  layout.tsx            # Root layout with fonts
/components
  chat.tsx              # Chat messages component
  prd-preview.tsx       # Markdown preview
  ui/                   # Reusable UI components
/lib
  prompts.ts            # System prompts
  rate-limit.ts        # IP-based limiting
```

## Key Implementation Details

### API Route (app/api/chat/route.ts)
```typescript
import { anthropic } from '@ai-sdk/anthropic';
import { streamText } from 'ai';

export async function POST(req: Request) {
  const { messages } = await req.json();
  
  const result = await streamText({
    model: anthropic('claude-3-5-sonnet-20241022'),
    system: PRDGeneral_PROMPT,
    messages,
    temperature: 0.7,
  });

  return result.toDataStreamResponse();
}
```

### Chat Hook Usage
```typescript
'use client';
import { useChat } from 'ai/react';

export default function Chat() {
  const { messages, input, handleSubmit, isLoading } = useChat();
  // UI implementation
}
```

## Performance Optimizations

- **Turbopack**: 96% faster hot reloading
- **Server Components**: Chat UI stays server-side
- **Streaming**: Progressive PRD generation
- **Static Landing**: Instant page load
- **Edge Runtime**: Optional for API routes

## What's NOT Included

- ❌ User accounts/auth
- ❌ Payment processing
- ❌ Conversation persistence
- ❌ Analytics (beyond Vercel defaults)
- ❌ Multiple conversation threads
- ❌ PRD editing capabilities

## Success Metrics (Week 1)

- Ship on time (7 days)
- 100+ unique visitors
- 25+ PRDs generated
- <500ms time to first token
- Zero critical bugs

## Post-MVP Roadmap

If validated:
1. Add Supabase for conversation history
2. Implement auth + usage limits
3. Stripe integration ($9/month unlimited)
4. PRD templates and customization
5. API for programmatic access

## Common Pitfalls to Avoid

1. **Don't expose API keys**: Keep them server-side only
2. **Don't over-engineer**: Ship the chat experience first
3. **Don't skip error handling**: Users will break things
4. **Don't forget mobile**: 40% will use phones
5. **Don't perfect prompts**: Iterate post-launch

---

**Complexity Score: 3/5** ✓