# Task ID: 6
# Title: Create PRD Preview Component
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Build markdown preview panel for real-time PRD generation with copy and download functionality, enhanced with Convex real-time subscriptions for improved streaming performance
# Details:
Create `/components/prd-preview.tsx` using Tailwind Typography for markdown rendering. Implement split-screen layout with chat on left and PRD preview on right. Add real-time streaming preview using Convex subscriptions for better performance and optimistic UI updates. Include copy to clipboard and download as .md file functionality. Make preview responsive - collapse to tabs on mobile. Add markdown parsing and syntax highlighting for code blocks. Focus on single-user experience with seamless real-time updates.

# Test Strategy:
Test markdown rendering with various PRD formats, verify copy/download functions work correctly, validate responsive behavior on mobile devices, ensure Convex real-time subscriptions work properly, and test optimistic UI updates during streaming

# Subtasks:
## 1. Setup Basic Markdown Rendering [pending]
### Dependencies: None
### Description: Create /components/prd-preview.tsx with @tailwindcss/typography for markdown display
### Details:
Install @tailwindcss/typography, create basic component structure, implement markdown parsing with proper styling, test with sample content

## 2. Implement Split-Screen Layout [pending]
### Dependencies: None
### Description: Build responsive layout with chat on left and preview on right
### Details:
Use CSS Grid/Flexbox for desktop split-screen, implement mobile-responsive tabs, handle window resizing and breakpoints

## 3. Add Convex Real-Time Subscriptions [pending]
### Dependencies: None
### Description: Integrate Convex real-time subscriptions for improved streaming performance
### Details:
Set up Convex queries/mutations for PRD data, implement real-time subscriptions for live updates, replace direct chat streaming with Convex-based updates, add error handling for connection issues

## 4. Implement Optimistic UI Updates [pending]
### Dependencies: 6.3
### Description: Add optimistic updates for better single-user experience during PRD generation
### Details:
Implement optimistic markdown rendering before Convex confirmation, add loading indicators and smooth transitions, handle rollback scenarios for failed updates, ensure UI remains responsive during generation

## 5. Implement Copy & Download Functionality [pending]
### Dependencies: None
### Description: Add copy to clipboard and download as .md file features
### Details:
Create copy button with success feedback, implement markdown file download with proper filename, add toast notifications, handle browser compatibility

## 6. Add Mobile Responsive Behavior [pending]
### Dependencies: None
### Description: Optimize preview component for mobile devices
### Details:
Implement tab-based switching on mobile, optimize touch interactions, ensure readability on small screens, test download on mobile browsers

## 7. Polish Preview & Handle Edge Cases [pending]
### Dependencies: 6.3
### Description: Add syntax highlighting and error handling for robust preview with Convex integration
### Details:
Implement code block syntax highlighting, handle malformed markdown, add loading states during Convex operations, create empty state UI, handle Convex connection failures gracefully

