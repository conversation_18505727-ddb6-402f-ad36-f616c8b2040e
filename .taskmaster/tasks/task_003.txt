# Task ID: 3
# Title: Create PRDGeneral System Prompt
# Status: done
# Dependencies: 2
# Priority: high
# Description: Develop and implement the PRDGeneral clarification engine system prompt for converting product ideas to PRDs
# Details:
Create `/lib/prompts.ts` with the PRDGeneral system prompt that enforces relentless clarification, single-purpose philosophy, and pushes back on feature creep. Implement two modes: clarification mode (default) and PRD generation mode (triggered by 'Generate PRD'). Include personality traits: direct, helpful, uncompromising on clarity. Define the 4-step process: specific problem, core feature, target user, success metrics. Add PRD generation template following Solo Entrepreneur Framework with 800-word limit.

# Test Strategy:
Test prompt variations with different product ideas, verify mode switching works correctly, validate PRD output quality and format, and ensure consistent clarification behavior

# Subtasks:
## 1. Design core prompt structure and framework [done]
### Dependencies: None
### Description: Create the foundational prompt architecture that defines the AI assistant's behavior, personality, and response patterns for PRD generation
### Details:
Design the base prompt template including system instructions, role definition, output format specifications, and behavioral guidelines. Establish consistent tone, style, and interaction patterns.

## 2. Implement mode switching logic system [done]
### Dependencies: 3.1
### Description: Develop the mechanism for transitioning between different operational modes (clarification, generation, refinement) based on user input and context
### Details:
Create conditional logic for detecting when to switch between modes, define triggers for each mode transition, and establish state management for maintaining context across mode changes.

## 3. Build clarification flow implementation [done]
### Dependencies: 3.1, 3.2
### Description: Implement the interactive clarification process that gathers missing requirements and validates user intent before PRD generation
### Details:
Design question sequences for gathering missing information, create validation logic for user responses, and implement iterative refinement loops until sufficient detail is collected.

## 4. Create PRD generation template system [done]
### Dependencies: 3.1, 3.2, 3.3
### Description: Develop the structured template and generation logic for producing comprehensive Product Requirements Documents from gathered requirements
### Details:
Design PRD template with sections for overview, objectives, features, technical requirements, and acceptance criteria. Implement generation logic that populates template with clarified requirements and maintains consistency.

