# Task ID: 1
# Title: Implement Advanced PRD Intent Classification Enhancements
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Add non-MVP modules—including a Problem-Solution Pattern Matcher, Product Type Classifier, and 4-Phase Chain of Verification (CoVe)—to enrich PRD intent understanding and handle edge cases.
# Details:
1. Architectural Placement
   • Extend the existing IntentClassifier service with a plug-in style interface (e.g., Strategy pattern) so advanced modules can be toggled on/off via a feature flag (ENV var ADVANCED_PRD=true).

2. Problem-Solution Pattern Matcher
   • Curate a dataset of PRD excerpts annotated with the canonical “problem → solution → benefit” structure.
   • Fine-tune a transformer (e.g., DeBERTa-v3) to output BEGIN/INSIDE/OUTSIDE tags for each span.
   • Post-process tagged spans into a JSON section {"problem":..., "solution":..., "benefit":...}.
   • Expose a /patterns endpoint that, given raw PRD text, returns detected structures and confidence scores.

3. Product Type Classifier Module
   • Multi-label classifier that predicts B2B, B2C, Mobile, Web, SaaS, Hardware, etc.
   • Use the same text encoder as the core model to minimise latency; add a sigmoid output layer with binary-cross-entropy loss.
   • Provide class weights to counter data imbalance and enable threshold tuning per label.

4. 4-Phase Chain of Verification (CoVe)
   • Phase 1: Syntactic validation – ensure required sections (overview, users, metrics) exist via regex+heuristics.
   • Phase 2: Semantic sanity – run predictions twice with dropout (MC Dropout) and flag if confidence variance > Θ.
   • Phase 3: Cross-module consensus – if core classifier and pattern matcher disagree, mark as "review_needed".
   • Phase 4: External rules engine – apply domain-specific rules (e.g., security requirements for B2B SaaS).
   • Implement CoVe as an asynchronous pipeline that annotates PRD objects with {"status":"ok"|"warning"|"error", "reasons":[...]}.

5. Integration
   • Update API documentation to include new endpoints and JSON schemas.
   • Add protobuf definitions if gRPC is used.
   • Ensure backward compatibility: existing clients receive identical responses when the ADVANCED_PRD flag is false.

6. Performance & Ops
   • Target <150 ms additional latency at P95.
   • Export Prometheus metrics: pattern_matcher_latency_ms, product_type_accuracy_rolling, cove_disagreements_total.
   • Provide Kubernetes Helm values to toggle individual sub-modules.

# Test Strategy:
Unit Tests
• Each module has ≥90 % code coverage.
• Snapshot tests verify JSON output formats.

Model Evaluation
• Hold-out dataset with 500 manually labelled PRDs.
  – Problem-Solution Matcher: F1 ≥0.83 overall.
  – Product Type Classifier: micro-F1 ≥0.88, per-label precision ≥0.8.
• Run k-fold cross-validation and attach a Jupyter notebook with results to the MR.

Integration Tests
• Spin up a local stack with ADVANCED_PRD=true.
• POST 20 sample PRDs and assert:
  – Response contains pattern_match block when detected.
  – product_types array matches golden data.
  – CoVe status=ok for standard PRD; status=warning when injected edge cases.

Regression & Performance
• k6 scenario issuing 100 RPS for 10 min; P95 latency increase ≤150 ms.

Manual QA
• Toggle feature flag off and confirm legacy behaviour.
• Review Prometheus dashboards for new metrics.

CI/CD Gates
• Block merge if any eval metric drops >2 % vs previous model or if latency budgets exceeded.

# Subtasks:
## 1. Implement Problem-Solution Pattern Matcher [pending]
### Dependencies: None
### Description: Encode common business context patterns to improve readiness detection and PRD outline suggestions
### Details:
• Build trie-based matcher for patterns like "We struggle with X" ⇒ Problem, "We propose Y" ⇒ Solution.
• Store matches in `conversation_patterns` table with offsets.
• Provide `suggestedSections` array (e.g., "Problem Statement", "Solution Overview") for generation prompt.
• Complexity: Medium
• Dependencies: Context-Aware Classification Engine from MVP
• Implementation Priority: 1st (good ROI, enhances PRD quality suggestions)

## 2. Implement Product Type Classifier Module [pending]
### Dependencies: None
### Description: Classify conversation into B2B/B2C/mobile/web/SaaS etc. to tailor PRD sections
### Details:
• Lightweight zero-shot Claude call with taxonomy prompt.
• Cache result in `conversation_metadata` (`productType`, `confidence`).
• Expose React hook `useProductType` for UI badges.
• Complexity: Medium
• Dependencies: Context-Aware Classification Engine from MVP
• Implementation Priority: 2nd (nice feature, enables tailored PRD sections)

## 3. Implement 4-Phase Chain of Verification (CoVe) [pending]
### Dependencies: None
### Description: Add graduated response system for edge cases: ask for clarification, re-check intent, escalate to human, or auto-resume
### Details:
• Store `covePhase` in `conversation_modes`.
• Phase 1: auto-clarify when confidence 0.4-0.6.
• Phase 2: re-run classification with expanded history.
• Phase 3: flag for human review via `support_tickets` table.
• Phase 4: auto-resume after human override.
• Implement `advanceCoVePhase` mutation invoked by detection pipeline.
• Complexity: High
• Dependencies: Multi-Category Indicator Collector & Context-Aware Classification Engine from MVP
• Implementation Priority: 3rd (sophisticated but overkill for MVP validation)

