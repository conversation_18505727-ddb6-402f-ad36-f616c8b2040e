# Task ID: 12
# Title: Optimize & Harden Convex Backend for Production
# Status: pending
# Dependencies: 11
# Priority: medium
# Description: Prepare the Convex backend for production by adding staging deployment, performance optimizations (pagination, indexing, load-test tuning), configuration management, CI/CD with rollback, and an exhaustive test suite.
# Details:
1. Staging & Prod Environments
   • Create two Convex deployments via `convex deploy staging` and `convex deploy prod`.
   • In the Convex dashboard, add distinct environment variables (ANTHROPIC_API_KEY, RATE_LIMIT, FEATURE_FLAGS) per deployment.
   • Annotate deployments with git SHA tags for traceability (`convex deploy --tag $GITHUB_SHA`).

2. CI/CD & Rollback
   • Add a GitHub Action workflow (`.github/workflows/convex.yml`) that:
     a. Runs unit tests & lints Convex functions.
     b. Deploys automatically to staging on every push to `main`.
     c. Prompts for a manual approval job that promotes the current staging build to production (`convex deploy prod --tag $GITHUB_SHA`).
   • Implement rollback script `scripts/convex-rollback.sh` that lists previous prod tags and redeploys (`convex deploy prod --tag TAG_TO_ROLLBACK`).

3. Performance & Pagination
   • Refactor heavy list queries (chat messages, PRD revisions) to use cursor-based pagination: `query({ after }) ⇒ paginate({ limit: 50 })`.
   • Add composite indexes in `convex/schema.ts` on `[conversationId, createdAt]` and `[userId, createdAt]`.
   • Use `internal` functions or batch mutations to reduce round-trips during streaming.
   • Cache common readonly queries with `convex.actionCache(‘5s’)` where appropriate.

4. Load Testing
   • Write Artillery scenario (`load.yml`) that simulates 100 concurrent users generating PRDs (clarification + generation cycles).
   • Stage run via `artillery run load.yml --target https://staging.prdgeneral.ai` and capture p95 latency, error rate.
   • Tune Convex deployment scale limits (compute + database) until p95 < 300 ms and error rate < 1 % at 100 RPS sustained.

5. Monitoring & Alerts
   • Enable Convex Observability dashboards and set alerts for error rate > 2 % or latency > 400 ms (Slack webhook).
   • Expose custom metric `prd_generated_total` via Convex logging and verify it appears in dashboard.

6. Comprehensive Test Suite
   • Unit tests: use `vitest` + `@convex/testing` for function-level tests (rate-limited mutation, paginated query returns expected cursors).
   • Integration tests: Playwright tests that walk through full clarification → generation flow hitting staging.
   • Smoke test script `npm run smoke:prod` executed post-deployment by CI.

7. Documentation
   • Add `docs/operating-convex.md` covering promotion, rollback, env variables, and disaster recovery.


# Test Strategy:
• Environment Verification: Run `convex deploy staging` and confirm separate URL & variables; promote to prod and check tag.
• CI Pipeline: Push dummy commit; ensure GitHub Action runs tests, deploys to staging, awaits manual approval, then deploys to prod.
• Rollback Drill: Deploy a failing commit to staging, promote to prod, run rollback script, and verify previous version restored.
• Load Test Pass Criteria: Execute Artillery load test; assert p95 latency < 300 ms and error rate < 1 %.
• Pagination Tests: Insert 120 messages, query with limit 50—assert three pages returned, cursors valid, no duplicate/ missing items.
• Index Effectiveness: Use Convex dashboard query profiler—confirm indexed query drops from >800 ms to <50 ms.
• End-to-End Tests: Playwright script generates PRD on staging; assert success status and correct markdown structure.
• Monitoring & Alerts: Manually trigger function error; confirm alert delivered to Slack within 1 minute.


# Subtasks:
## 1. Deploy to Staging & Verification [pending]
### Dependencies: 11.5
### Description: Push migrated backend to staging environment and run end-to-end tests.
### Details:
Automate deployment with CI, seed test data, validate auth, data integrity, and real-time flows across devices.

## 2. Perform Load & Performance Testing [pending]
### Dependencies: 10, 11.6
### Description: Stress-test Convex backend under expected and peak loads.
### Details:
Use k6 or Artillery to simulate concurrent users, monitor latency, throughput, and resource usage, and optimize indexes/functions as needed.

## 3. Create Rollback & Contingency Plan [pending]
### Dependencies: 10, 11.6, 11.7
### Description: Document and automate safe rollback procedures in case of production issues.
### Details:
Define data export/import scripts, versioned deployment tags, traffic switching steps, and communication plan with stakeholders.

## 4. Add Pagination and Performance Features [pending]
### Dependencies: 10, 11.3
### Description: Implement pagination for message queries and add performance monitoring capabilities
### Details:
Add pagination support to message queries to handle large chat histories efficiently. Implement performance monitoring with timing and usage metrics collection. Add caching for mode detection results to improve performance.

## 5. Configuration Management and Code Quality [pending]
### Dependencies: 10, 11.3
### Description: Externalize system prompts and refactor hardcoded values to configuration
### Details:
Move system prompts from code to configuration files or database for easier management. Extract hardcoded thresholds and magic numbers to configuration files. Add enhanced access control with IP-based restrictions for anonymous users.

## 6. Comprehensive Testing Suite [pending]
### Dependencies: 10, 11.2, 11.3
### Description: Create unit tests for mode detection, validation logic, and critical bug fixes
### Details:
Implement unit tests for mode detection and validation logic. Add tests for user auto-creation functionality. Create tests for session ID uniqueness validation. Add integration tests for rate limiting and error handling.

