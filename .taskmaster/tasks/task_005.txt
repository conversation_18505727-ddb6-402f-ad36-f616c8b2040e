# Task ID: 5
# Title: Implement PRD Generation Mode Detection
# Status: in-progress
# Dependencies: None
# Priority: medium
# Description: Add logic to detect when user requests PRD generation and switch Claude from clarification to generation mode using Convex functions
# Details:
Implement Convex functions to detect keywords like 'Generate PRD', 'Create PRD', or similar phrases. Use Convex's TypeScript-first approach to create real-time mode switching functionality with automatic state synchronization. Leverage Convex queries and mutations for conversation state management. Create client-side detection components that utilize Convex's reactive queries to show 'Generate PRD' button when appropriate. Store conversation state in Convex database to determine when sufficient clarification has occurred.

# Test Strategy:
Test various trigger phrases for PRD generation using Convex function testing, verify conversation context is maintained during mode switch through Convex state management, validate PRD generation includes previous clarification details stored in Convex database

# Subtasks:
## 1. Build PRD Intent Prompt Template & Keyword Library [done]
### Dependencies: None
### Description: Create a structured prompt-engineering template that specifies the exact "Trigger this action when" patterns and seed keywords signalling PRD generation intent.
### Details:
• Author `/lib/prdIntentTemplate.ts` exporting a constant object with two sections: `clarificationMode` and `generationMode`.
• Populate `keywords.primary` (e.g., ["generate prd", "create prd", "draft prd"]) and `keywords.secondary` (synonyms, pluralisations, misspellings) loaded from `PromptEngineeringforPRDIntent.md`.
• Define a TypeScript `matchIntent(text: string): {intent: "clarification" | "generation"; rule: string}` utility using RegExp with word-boundary patterns and case-insensitive flags.
• Include a confidence threshold field per keyword group (default 0.8) that later components can read.

## 2. Implement Convex Real-time Keyword Detection Mutation [pending]
### Dependencies: 5.1
### Description: Create a Convex mutation that receives every incoming chat turn, runs `matchIntent`, and writes an `intentEvent` row to the `conversation_intents` table.
### Details:
• File: `convex/intents.ts` with `export const detectIntent = mutation`.
• Schema: `{ _id, conversationId, turnIndex, detectedIntent, ruleApplied, confidence }`.
• Use Convex's type-safe `v.object` validators.
• Confidence <0.5 => store `detectedIntent: "unknown"` for later CoVe escalation.
• Expose via `useMutation("detectIntent")` on client for each user message.

## 3. Create Multi-Category Indicator Collector & Scorer [cancelled]
### Dependencies: 5.1, 5.2
### Description: Track requirement, technical, business, and process indicators per turn; compute rolling confidence that user is ready for PRD generation.
### Details:
• Add `conversation_indicators` table with numeric scores per category.
• Build `updateIndicators` mutation that increments category scores when certain n-grams appear (list sourced from research doc).
• Export `getIndicatorScore` query returning a weighted sum (weights in config file) and a normalized 0-1 confidence.
• Persist a `generationReadiness` boolean when score >0.75.

## 4. Develop Context-Aware Classification Engine [cancelled]
### Dependencies: 5.2, 5.3
### Description: Analyze full conversation history, role/persona metadata, and previous intents to classify current mode with higher reliability.
### Details:
• Implement `classifyContext` serverless function (Edge runtime) that pulls last N turns via Convex query `historyForClassification`.
• Pass messages + persona to Claude using a low-temperature (0.2) call with a system prompt: "Return JSON {intent, confidence}".
• Cache result in `conversation_modes` table with TTL 5 min.
• Combine engine confidence with indicator score using logistic regression weights stored in Convex config.

## 5. Implement Conversation State Schema & Turn Counter [pending]
### Dependencies: 5.2
### Description: Persist mode, turn count, topic shifts, and compressed context snapshots in Convex for real-time synchronization across clients.
### Details:
• Extend `conversations` table: `mode`, `turnCount`, `topicHash`, `compressedHistory` (Gzip+Base64 string).
• Create `incrementTurn` mutation that updates counters and triggers history compression every 15 turns.
• Provide `useConversationState(conversationId)` React hook built on Convex reactive query.

## 7. Build Confidence Scoring Service [cancelled]
### Dependencies: 5.3
### Description: Implement Yes-Score method combining self-assessment prompting and multi-response consistency checks.
### Details:
• Create `yesScore` utility: call Claude twice with slightly varied system prompts, compute Jensen-Shannon divergence of probabilities.
• Self-assessment: add "On a scale 0-1 how certain are you?" forcing model reflection.
• Final score = geometric mean of divergence complement and self-assessment.
• Expose via Convex query `getYesScore` for other modules.

## 10. Build Edge Case Manager & Fallback Logic [cancelled]
### Dependencies: 5.7
### Description: Handle ambiguous, boundary, or incomplete information before mode switch to prevent premature PRD generation.
### Details:
• Create `shouldHaltGeneration` util reading CoVe phase, yesScore, and indicator thresholds.
• If true, insert system message asking targeted questions.
• Provide `fallbackReason` field for analytics.
<info added on 2025-07-26T04:05:39.100Z>
Dependencies updated: rely solely on 5.7 (Confidence Scoring Service). For MVP, drop all CoVe phase checks and the 4-Phase Chain of Verification; `shouldHaltGeneration` now consumes the single `yesScore` (plus optional `uncertaintyScore`) returned by 5.7, compares it to a configurable `EDGE_CASE_THRESHOLD`, and decides whether to trigger a targeted follow-up system message. Continue to populate `fallbackReason` (values: `low_confidence`, `missing_context`) for analytics.
</info added on 2025-07-26T04:05:39.100Z>

## 11. Integrate Real-time UI Components for Mode Switching [cancelled]
### Dependencies: 5.2, 5.5
### Description: Use Convex reactive queries to surface a "Generate PRD" button or automatic switch when all readiness conditions pass.
### Details:
• Component `/components/GeneratePrdButton.tsx` subscribes to `conversation_modes` and `generationReadiness`.
• Auto-switch: when `mode!="generation"` && confidence >0.9, call `mutate("setMode", {mode:"generation"})`.
• Animate button with progress ring reflecting readiness percentage.
• Ensure optimistic UI updates using Convex optimistic updates API.

## 12. Create Comprehensive Testing & Validation Framework [cancelled]
### Dependencies: 5.1, 5.2, 5.3, 5.4
### Description: Automate accuracy, edge case, and performance tests across all classification components before production deploy.
### Details:
• Add `tests/intentSuite.spec.ts` integrating Jest + Playwright.
• Load dataset from `/fixtures/classificationCorpus.json` with labels.
• Run nightly CI job measuring precision/recall, latency, and memory usage; fail build if precision <0.93.
• Generate HTML coverage and post results to Slack via webhook.

## 13. Create Clarification Specialist Agent [pending]
### Dependencies: None
### Description: Build dedicated agent for 4-step clarification process using Convex Agent framework. Single purpose: masters rigorous clarification, pushes back on vagueness. Input: raw user idea. Output: structured clarification object. 3-5 minute focused interaction. Reference: ./docs/convex/agents/workflows/README.md for implementation patterns. [Updated: 7/26/2025]
### Details:
<info added on 2025-07-26T22:52:45.281Z>
Clarification Agent design aligned with Unix philosophy

Objective  
Guarantee that every feature entering the PRD pipeline is reduced to a single, clearly articulated purpose—“do one thing and do it well”—before PRD generation begins.

Core Responsibilities  
• Interrogate incoming feature requests to isolate the primary function and eliminate or defer all secondary capabilities.  
• Generate a concise “Unix Core Objective” (≤140 chars) to be stored with the feature’s metadata.  
• Detect scope‐creep keywords (e.g., “also,” “while,” “multiple,” “dashboard,” “platform”) and automatically trigger follow-up questions or recommend ticket splits.  
• Maintain a decision log listing removed/deferred functions for future backlog consideration.

Interaction Flow  
1. Receive raw feature description.  
2. Prompt: “What is the single outcome this feature must deliver exceptionally well?”  
3. Present any additional functions detected; request user confirmation to remove, defer, or create separate tickets.  
4. Iterate until one-sentence Unix Core Objective is finalized.  
5. Pass the sanitized feature plus decision log to downstream PRD Generation Mode Detection.

Acceptance Criteria  
• Each feature has exactly one Unix Core Objective saved.  
• No retained requirement references more than one functional domain.  
• Scope-creep detection accuracy ≥90% in test set.  
• User confirmation flow completes in ≤3 clarification turns 80% of the time.

Implementation Notes  
Leverage LLM prompt chaining with regex-based scope-creep detector; integrate as a middleware service invoked immediately after idea intake and before specialist agent hand-off.
</info added on 2025-07-26T22:52:45.281Z>

## 14. Create PRD Generator Specialist Agent [pending]
### Dependencies: None
### Description: Build dedicated agent for Solo Entrepreneur Framework PRD creation. Single purpose: transforms clarified requirements into 800-word professional PRDs. Input: structured clarification data. Output: formatted PRD document. 2-3 minute generation time. Reference: ./docs/convex/agents/workflows/README.md for agent setup.
### Details:


## 15. Implement Two-Agent Sequential Workflow [pending]
### Dependencies: 5.13, 5.14
### Description: Build Convex workflow that orchestrates clarification → generation handoff. Define mvpPRDWorkflow with step.runAction calls for each agent. Include retry logic and error handling for agent transitions. Target 5-8 minute total processing time. Follow patterns in ./docs/convex/agents/workflows/README.md for workflow definition and step orchestration.
### Details:


## 16. Create Agent State Management & Handoff Logic [pending]
### Dependencies: 5.15
### Description: Implement Convex mutations for managing agent transitions, storing clarification results, and passing structured data between agents. Ensure 95%+ successful handoff rate with proper error handling and fallback to single-agent mode. Reference ./docs/convex/agents/workflows/README.md for state management patterns.
### Details:


## 17. Build Multi-Agent Progress UI Components [pending]
### Dependencies: 5.15
### Description: Create React components showing two-step process: '🔍 Clarifying requirements...' and '📝 Generating PRD...'. Use existing chat interface with progress indicators. Show agent handoff and estimated completion times.
### Details:


## 18. Integrate Workflow with Existing Chat Interface [pending]
### Dependencies: 5.16, 5.17
### Description: Wire mvpPRDWorkflow into existing useStreamingChat hook and Chat components. Maintain current UX while adding multi-agent orchestration. Ensure workflow triggers properly from user input. Reference ./docs/convex/agents/workflows/README.md for workflow integration patterns.
### Details:


## 19. Implement Agent Quality Comparison System [deferred]
### Dependencies: 5.18
### Description: Build A/B testing framework to compare single-agent vs two-agent results. Measure PRD quality score, user completion rate, and processing time. Target 20%+ improvement in completion rate.
### Details:


## 20. Create Multi-Agent End-to-End Testing Suite [deferred]
### Dependencies: 5.18
### Description: Test complete workflow: user input → clarification agent → PRD generator → final output. Validate agent handoff, error handling, and fallback scenarios. Ensure 95%+ workflow completion rate. Use workflow testing patterns from ./docs/convex/agents/workflows/README.md.
### Details:


## 21. Optimize Agent Response Times & Resource Usage [deferred]
### Dependencies: 5.18
### Description: Monitor and optimize agent performance to hit 3-5 minute clarification + 2-3 minute generation targets. Implement proper timeout handling and resource management for concurrent agent operations.
### Details:


## 22. Add Multi-Agent Error Handling & Fallback Logic [pending]
### Dependencies: 5.16
### Description: Implement robust error handling for agent failures, timeout scenarios, and handoff issues. Create fallback to single-agent mode when multi-agent workflow fails. Ensure graceful degradation. Reference ./docs/convex/agents/workflows/README.md for error handling best practices.
### Details:


## 23. Implement Control Group Conversational DNA & Behavioral Patterns [pending]
### Dependencies: None
### Description: Add the exact interaction patterns from control group to Clarification Agent: aggressive scope enforcement ('SCOPE CREEP DETECTION!'), vagueness rejection ('STOP. Too vague'), relentless questioning structure ('Question 1:', 'Question 2:'), Unix philosophy enforcement ('Pick ONE', 'do one thing well'), progress status system ('[Clarification Engine: ACTIVE]', '[Focus Validator: ARMED]'), and celebration patterns ('EXCELLENT! 🎯', '🔒 DEFINITION LOCKED!'). Reference control group chat examples for exact phrasing and tone. Agent must refuse vague inputs, push back on feature creep, and guide users through systematic clarification rounds until definition lock achieved.
### Details:


