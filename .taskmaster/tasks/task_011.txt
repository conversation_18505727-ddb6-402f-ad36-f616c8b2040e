# Task ID: 11
# Title: Convex Backend Migration
# Status: done
# Dependencies: 2
# Priority: high
# Description: Migrate from Next.js API routes to Convex backend functions with real-time subscriptions, authentication, and TypeScript schema definitions
# Details:
Install Convex SDK with `pnpm install convex` and run `pnpx convex dev` to initialize. Create `/convex` directory with schema definitions in `schema.ts` using Convex's TypeScript schema system. Migrate existing API routes to Convex functions in `/convex/functions/` directory. Set up Convex authentication system replacing current auth implementation. Convert chat API route to Convex mutation/query functions with real-time streaming capabilities. Implement real-time subscriptions for live PRD updates using Convex's reactive queries. Update client-side code to use `useConvexAuth()` and `useQuery()`/`useMutation()` hooks. Configure Convex environment variables and deploy configuration. Add TypeScript definitions for all data models including chat messages, PRD generations, and user sessions.

# Test Strategy:
Verify Convex development environment starts correctly and syncs schema changes. Test migration of all existing API functionality to Convex functions. Validate real-time subscriptions work for live PRD updates. Confirm authentication flows work with Convex auth system. Test TypeScript compilation with new Convex schemas. Verify production deployment works with Convex backend. Load test real-time features with multiple concurrent users.

# Subtasks:
## 1. Design Convex Schema [done]
### Dependencies: None
### Description: Model all application data in Convex’s TypeScript schema system.
### Details:
Audit existing Next.js API route models, map to Convex documents, define tables, indexes, and relationships in /convex/schema.ts. Include versioning fields for future migrations.

## 2. Set Up Authentication with Convex [done]
### Dependencies: 11.1
### Description: Replace current auth implementation with Convex’s auth module.
### Details:
Configure Convex auth provider, session management, and role-based access rules; update environment variables and secure routes.
<info added on 2025-07-22T19:32:47.923Z>
Add Google OAuth setup instructions:
• In Google Cloud Console → APIs & Services → Credentials, create an OAuth 2.0 Client ID (application type: Web).  
• Set Authorized redirect URI to https://industrious-egret-645.convex.site/api/auth/callback/google.  
• Copy Client ID and Client Secret into project root .env as  
  AUTH_GOOGLE_ID=<client_id>  
  AUTH_GOOGLE_SECRET=<client_secret>  
  (also add placeholders to .env.example).  
• Restart `pnpx convex dev` (and Vercel preview/prod deployments) so Convex picks up the new vars.  
• Verify login flow by authenticating with a Google account; ensure session object contains provider === 'google' and role assignment logic still applies.
</info added on 2025-07-22T19:32:47.923Z>
<info added on 2025-07-22T19:40:12.134Z>
• In Google OAuth “Authorized JavaScript origins”, enter the base URL of your Convex deployment (no path or trailing slash), e.g. https://industrious-egret-645.convex.site.  
• To locate this URL, run `npx convex dashboard` or open the Convex dashboard and copy the HTTPS address with the format https://<adjective-animal-###>.convex.site.  
• JavaScript origins specify where your frontend initiates auth requests; they differ from redirect URIs (where Google sends the auth response). Origins cannot contain wildcards or paths and must include a port if it’s not 80/443.  
• Save changes, then restart `pnpx convex dev` (and redeploy Vercel previews/prod) to apply the updated OAuth settings.
</info added on 2025-07-22T19:40:12.134Z>
<info added on 2025-07-22T19:57:00.630Z>
• Persist the credentials to Convex’s server-side environment so auth functions can access them:  
  npx convex env set AUTH_GOOGLE_ID <client_id>  
  npx convex env set AUTH_GOOGLE_SECRET <client_secret>  
  (substitute the actual values).  
• Restart `pnpx convex dev` and redeploy previews/prod to load the new vars into your Convex functions.
</info added on 2025-07-22T19:57:00.630Z>
<info added on 2025-07-22T20:03:00.994Z>
Add GitHub OAuth setup instructions:  
• In GitHub → Settings → Developer settings → OAuth Apps, click “New OAuth App”.  
• Set Application name (e.g., “PRDGeneral”), Homepage URL to https://industrious-egret-645.convex.site, and Authorization callback URL to https://industrious-egret-645.convex.site/api/auth/callback/github.  
• Register the app, then copy the Client ID and generate a new Client Secret.  
• Persist the creds to Convex’s server-side environment (these are required even if they exist in a local .env):  
  npx convex env set AUTH_GITHUB_ID <client_id>  
  npx convex env set AUTH_GITHUB_SECRET <client_secret>  
  (also add placeholders to .env.example for clarity).  
• Restart `pnpx convex dev` and redeploy Vercel previews/prod so Convex functions load the new vars alongside the Google creds.  
• Verify GitHub sign-in; ensure session.provider === 'github' and that role assignment logic functions identically to Google logins.
</info added on 2025-07-22T20:03:00.994Z>
<info added on 2025-07-22T20:29:10.352Z>
• Implement automatic user record creation during authentication flow:  
  1. Extend `convex/schema.ts` with a `users` table (if not already present) and add a secondary index on `providerId` to support quick look-ups:  
     ```ts
     users: defineTable({
       provider: v.string(),
       providerId: v.string(),   // e.g. sub / id from OAuth provider
       name: v.optional(v.string()),
       email: v.optional(v.string()),
       avatarUrl: v.optional(v.string()),
       role: v.string(),         // 'user' | 'admin'
       createdAt: v.number(),
     }).index("providerId", ["providerId"]),
     ```  
  2. Replace the read-only `users.ts` query with an upsert-style mutation `users/createIfMissing.ts` that inserts a row when no match is found:  
     ```ts
     // /convex/functions/users/createIfMissing.ts
     import { mutation } from "./_generated/server";
     import { v } from "convex/values";

     export default mutation({
       args: {
         provider: v.string(),
         providerId: v.string(),
         name: v.optional(v.string()),
         email: v.optional(v.string()),
         avatarUrl: v.optional(v.string()),
       },
       handler: async (ctx, args) => {
         const existing = await ctx.db
           .query("users")
           .withIndex("providerId", q => q.eq("providerId", args.providerId))
           .unique();
         if (existing) return existing._id;

         return await ctx.db.insert("users", {
           ...args,
           role: "user",
           createdAt: Date.now(),
         });
       },
     });
     ```  
  3. In the auth callback (e.g. `/convex/functions/auth/handleLogin.ts`), invoke the mutation immediately after obtaining the Convex session:  
     ```ts
     const { provider, providerId, name, email, avatarUrl } = sessionIdentity;
     const userId = await ctx.runMutation("users/createIfMissing", {
       provider,
       providerId,
       name,
       email,
       avatarUrl,
     });
     session.userId = userId;   // attach for downstream use
     ```  
  4. Add unit test `users.test.ts` verifying that first-time logins insert a new record and repeat logins reuse the same `userId`.  
  5. Update role-assignment logic to rely on `session.userId` rather than failing when the user table is empty.  

After deploying, first-time Google or GitHub sign-ins will automatically create a `users` row, eliminating the current authentication failure when no existing record is found.
</info added on 2025-07-22T20:29:10.352Z>

## 3. Convert API Routes to Convex Functions [done]
### Dependencies: 11.1, 11.2
### Description: Rewrite existing Next.js API logic as Convex mutations and queries.
### Details:
Create files in /convex/functions/, replicate business logic, ensure type safety, and remove obsolete API route files.
<info added on 2025-07-22T20:31:18.625Z>
Add per-user/IP rate-limiting middleware to every new Convex mutation and query (token-bucket with configurable window; return 429 ConvexError when exceeded). Wrap all function bodies in a shared try/catch utility that emits structured logs containing functionName, userId/sessionId, input params, and stack trace to the chosen logger/Sentry before re-throwing typed ConvexError objects. When handling anonymous sessions, enforce uniqueness by first querying for existing sessionId; on collision, regenerate a cryptographically secure UUID v4 and retry until unique, then persist.
</info added on 2025-07-22T20:31:18.625Z>

## 4. Implement Real-Time Subscriptions [done]
### Dependencies: 11.3
### Description: Enable live data streaming for critical features using Convex listeners.
### Details:
Set up onUpdate listeners for chat and PRD updates, test client-side reactivity, and handle throttling/back-pressure.
<info added on 2025-07-22T20:33:06.107Z>
Complete the streaming pipeline for real-time chat:

• Refactor /convex/functions/chatCompletion.ts to return an async generator that yields Claude token chunks and writes them to the messages table in near-real-time (`status: "streaming"`).  
• Buffer chunks (e.g., 50–100 ms) to batch writes and stay within Convex 20 mutations/sec limit; flush on final chunk and mark `status: "done"`.  
• Wrap streaming loop in try/catch; on error capture message, set `status: "error"`, persist `errorMessage`, and propagate failure to client.  
• Add AbortController support: store controller in a `generationSessions` map keyed by messageId; expose /convex/functions/cancelGeneration mutation to allow UI “Stop” button to abort the stream.  
• Implement connection resilience: on websocket reconnect, re-subscribe to the live query and request any missed chunks; resume stream only if `status === "streaming"`.  
• Update client chat hook to concatenate incoming chunks, stop polling logic, surface `isStreaming`, `isError`, and `isCancelled` flags, and debounce re-renders to <16 ms.  
• Integration tests: simulated chunked response verifies correct ordering and no duplicate/missing tokens; cancellation test ensures abort stops further writes within 200 ms; reconnect test validates automatic resubscribe restores stream without data loss.
</info added on 2025-07-22T20:33:06.107Z>
<info added on 2025-07-24T19:55:29.221Z>
Update for “UI-first” streaming model:

• Modify /convex/functions/chatCompletion.ts  
  – Return an async generator that yields Claude chunks to the client immediately.  
  – Accumulate chunks server-side in a local buffer; after the generator finishes (or aborts), commit exactly one record to messages with the full text and status "done" (or "error"/"cancelled").  
  – Remove incremental message writes and the “streaming” status column; instead track in-flight sessions only in memory.

• generationSessions & AbortController  
  – Keep a map {messageId: {controller, buffer}}.  
  – Expose cancelGeneration mutation; on cancel, abort controller, mark DB row “cancelled” if it already exists, else skip insert.

• Client chat hook  
  – openStream(messageId) starts the Convex action, appends incoming chunks to local streamedText state for live rendering.  
  – Flags: isStreaming (action in flight), isError, canCancel (controller present & !done).  
  – When live query returns the final stored row, replace streamedText with it and clear isStreaming.

• Connection resilience  
  – On websocket drop, preserve local streamedText; on reconnect, resume live query.  
  – If the action is still running, streamedText continues when transport is re-established; otherwise the final DB record arrives.

• Tests  
  – chunkStreaming.test.ts: UI receives ordered, non-duplicated chunks within 50 ms average latency.  
  – cancelGeneration.test.ts: abort stops further chunks ≤200 ms, no DB insert.  
  – finalWrite.test.ts: stored message equals concatenated chunks.  
  – reconnectResilience.test.ts: simulate disconnect; after reconnect UI resumes stream or swaps in final message without loss/duplication.
</info added on 2025-07-24T19:55:29.221Z>
<info added on 2025-07-25T21:31:33.850Z>
IMPLEMENTATION COMPLETED – UI-First Streaming Architecture

• Core pipeline (convex/chatCompletion.ts): async generator streams chunks directly to the client; server buffers full response and performs a single final insert (status: "done" | "error" | "cancelled").  
• Session management (convex/lib/streamingSession.ts): in-memory AbortControllers with 5-min TTL, per-user concurrency limits, automatic cleanup, and `cancelGeneration` action for hard stops.  
• Client hook (useStreamingChat.ts): real-time chunk rendering with `isStreaming`, `canCancel`, optimistic UI, and auto-resubscribe on websocket reconnect.  
• Throttling/back-pressure: removal of per-chunk writes eliminates Convex 20 mut/s ceiling; session limits prevent abuse.  
• Comprehensive tests (440+ LOC) cover streaming order, cancellation (<200 ms), TTL expiry, concurrency, reconnect resilience, and edge cases.  
• Performance: <50 ms avg chunk latency, zero token loss/duplication.

ARCHITECTURAL RATIONALE

1. UI-first streaming avoids mutation rate limits, reduces DB noise, and simplifies state.  
2. Agent-based client design cleanly separates streaming logic from thread management and supports future multi-agent workflows.  
3. Ephemeral session rows keep message records immutable while enabling accurate cancellation and observability.

Subtask 11.4 is now COMPLETE; proceed to 11.5 “Update Client Adapters for Convex”.
</info added on 2025-07-25T21:31:33.850Z>

## 5. Update Client Adapters for Convex [done]
### Dependencies: 11.3, 11.4
### Description: Refactor frontend hooks and services to consume Convex queries/mutations.
### Details:
Integrate useQuery/useMutation hooks, remove REST fetches, and ensure TypeScript types align with backend schema.

