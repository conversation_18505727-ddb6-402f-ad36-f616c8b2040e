{"master": {"tasks": [{"id": 1, "title": "Setup Next.js 15.3 Project with Turbopack", "description": "Initialize the PRDGeneral MVP project with Next.js 15.3, TypeScript, Tailwind CSS, and Turbopack for rapid development", "details": "Run `npx create-next-app@latest PRDGeneral --turbo --typescript --tailwind --app` to create the project structure. Install required dependencies: `npm install ai @ai-sdk/anthropic @tailwindcss/typography`. Configure Turbopack for development with `next dev --turbo`. Set up TypeScript 5.5+ configuration and ensure React 19 compatibility with Next.js 15.3. Create basic file structure with /app, /components, and /lib directories.", "testStrategy": "Verify project starts successfully with Turbopack, TypeScript compilation passes, Tailwind CSS loads correctly, and all dependencies are properly installed without conflicts", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "Implement Claude API Integration", "description": "Set up server-side Claude 3.5 Sonnet integration using Vercel AI SDK 4.2+ with streaming capabilities", "details": "Create `/app/api/chat/route.ts` using Vercel AI SDK's `streamText` function with `@ai-sdk/anthropic` provider. Configure Claude 3.5 Sonnet model (`claude-3-5-sonnet-20241022`) with temperature 0.7. Implement POST endpoint that accepts messages array and returns streaming response. Add environment variable for ANTHROPIC_API_KEY. Ensure server-side only implementation with no client-side API key exposure.", "testStrategy": "Test API endpoint with <PERSON> or <PERSON><PERSON>, verify streaming responses work correctly, confirm API keys are not exposed in client-side code, and validate error handling for invalid requests", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Environment Setup and Configuration", "description": "Set up environment variables, configuration files, and project structure for API integration", "dependencies": [], "details": "Create .env files for API keys, set up configuration management, establish proper directory structure for API routes and middleware", "status": "done", "testStrategy": ""}, {"id": 2, "title": "API Route Creation and Structure", "description": "Create the basic API route structure and endpoint definitions", "dependencies": [1], "details": "Define API endpoints, set up routing middleware, create request/response schemas, implement basic route handlers", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Anthropic SDK Configuration and Integration", "description": "Configure and integrate the Anthropic SDK with proper authentication", "dependencies": [1, 2], "details": "Install Anthropic SDK, configure API client with authentication, set up connection handling and SDK initialization", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Streaming Implementation", "description": "Implement streaming response functionality for real-time API responses", "dependencies": [3], "details": "Set up streaming response handlers, implement chunk processing, configure client-side streaming reception, handle stream lifecycle management", "status": "done", "testStrategy": ""}, {"id": 5, "title": "Error Handling and Security Measures", "description": "Implement comprehensive error handling and security measures for API protection", "dependencies": [4], "details": "Add API key validation, implement rate limiting, set up error logging and monitoring, add input sanitization, configure CORS and security headers", "status": "done", "testStrategy": ""}]}, {"id": 3, "title": "Create PRDGeneral System Prompt", "description": "Develop and implement the PRDGeneral clarification engine system prompt for converting product ideas to PRDs", "details": "Create `/lib/prompts.ts` with the PRDGeneral system prompt that enforces relentless clarification, single-purpose philosophy, and pushes back on feature creep. Implement two modes: clarification mode (default) and PRD generation mode (triggered by 'Generate PRD'). Include personality traits: direct, helpful, uncompromising on clarity. Define the 4-step process: specific problem, core feature, target user, success metrics. Add PRD generation template following Solo Entrepreneur Framework with 800-word limit.", "testStrategy": "Test prompt variations with different product ideas, verify mode switching works correctly, validate PRD output quality and format, and ensure consistent clarification behavior", "priority": "high", "dependencies": [2], "status": "done", "subtasks": [{"id": 1, "title": "Design core prompt structure and framework", "description": "Create the foundational prompt architecture that defines the AI assistant's behavior, personality, and response patterns for PRD generation", "dependencies": [], "details": "Design the base prompt template including system instructions, role definition, output format specifications, and behavioral guidelines. Establish consistent tone, style, and interaction patterns.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Implement mode switching logic system", "description": "Develop the mechanism for transitioning between different operational modes (clarification, generation, refinement) based on user input and context", "dependencies": [1], "details": "Create conditional logic for detecting when to switch between modes, define triggers for each mode transition, and establish state management for maintaining context across mode changes.", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Build clarification flow implementation", "description": "Implement the interactive clarification process that gathers missing requirements and validates user intent before PRD generation", "dependencies": [1, 2], "details": "Design question sequences for gathering missing information, create validation logic for user responses, and implement iterative refinement loops until sufficient detail is collected.", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Create PRD generation template system", "description": "Develop the structured template and generation logic for producing comprehensive Product Requirements Documents from gathered requirements", "dependencies": [1, 2, 3], "details": "Design PRD template with sections for overview, objectives, features, technical requirements, and acceptance criteria. Implement generation logic that populates template with clarified requirements and maintains consistency.", "status": "done", "testStrategy": ""}]}, {"id": 4, "title": "Build Core Chat Interface", "description": "Implement the main chat UI using Vercel AI SDK's useChat hook with message display and input handling", "details": "Create `/app/chat/page.tsx` as the main chat interface using `useChat` hook from 'ai/react'. Implement message rendering with user/assistant distinction, auto-scrolling, and loading states. Create `/components/chat.tsx` for reusable chat components. Add streaming message display with progressive text rendering. Include input field with send button and keyboard shortcuts (Enter to send). Implement responsive design for mobile and desktop.", "testStrategy": "Test message sending and receiving, verify streaming works smoothly, validate responsive design on different screen sizes, and ensure proper loading states and error handling", "priority": "high", "dependencies": [3], "status": "done", "subtasks": [{"id": 1, "title": "Set up basic chat layout structure", "description": "Create the foundational layout components for the chat interface including header, message container, and input area", "dependencies": [], "details": "Design and implement the core layout structure with proper CSS Grid or Flexbox positioning. Include chat header, scrollable message area, and fixed input section at bottom. Ensure proper height calculations and overflow handling.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Integrate useChat hook for message management", "description": "Implement the useChat hook to handle message state, streaming, and chat session management", "dependencies": [1], "details": "Set up useChat hook from AI SDK to manage message array, handle streaming responses, and maintain chat state. Configure proper error handling and loading states for AI responses.", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Build message rendering system with streaming support", "description": "Create reusable message components that support real-time streaming and different message types", "dependencies": [2], "details": "Develop Message and MessageList components that can render user and assistant messages. Implement streaming text display with typewriter effect, proper markdown rendering, and message timestamps. Handle different message states (pending, streaming, complete).", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Implement input handling with keyboard shortcuts", "description": "Create chat input component with send functionality and keyboard shortcuts for better UX", "dependencies": [2], "details": "Build ChatInput component with textarea auto-resize, Enter to send (Shift+Enter for new line), proper form submission handling, and integration with useChat hook. Include send button and loading states.", "status": "done", "testStrategy": ""}, {"id": 5, "title": "Add responsive design and mobile optimization", "description": "Ensure chat interface works seamlessly across different screen sizes and devices", "dependencies": [3, 4], "details": "Implement responsive breakpoints, mobile-first design approach, touch-friendly input areas, and proper viewport handling. Optimize message display for mobile screens and ensure smooth scrolling behavior.", "status": "done", "testStrategy": ""}]}, {"id": 5, "title": "Implement PRD Generation Mode Detection", "description": "Implement the Main Hierarchical Cascade Flow for PRD generation mode detection and user input validation. Replace the former multi-agent approach with a sequential, gate-based system that evaluates user messages through four validation gates and short-circuits on first failure.", "status": "in-progress", "dependencies": [], "priority": "medium", "details": "Build a sequential gate pipeline using Convex TypeScript functions:\n1. Vagueness Gate – FAILS when technical jargon or acronyms appear without context. Generates a clarifying question requesting layman wording or added context.\n2. Focus Gate – FAILS when the request mixes multiple products or disparate ideas. Responds asking the user to pick a single product focus.\n3. Scope Gate – FAILS when feature lists extend beyond a minimal lovable product (MLP). Pushes back on feature creep and requests trimmed scope.\n4. Specificity Check – Runs a refinement loop (max 3 iterations) that ensures concrete details on problem, core feature, target user, and success metrics. If still inadequate after the loop, returns a final failure message; otherwise PASS.\n\nImplementation requirements:\n• Each gate is a pure Convex function that returns { pass: boolean; message?: string }.\n• mainCascadeFlow mutation invokes gates in order and exits early on first failure, storing the gate result and conversation state in Convex DB.\n• Successful passage of all gates flips the conversation state flag `mode = \"generation\"`, triggering PRD generation.\n• Client-side React component subscribes to conversation state via reactive Convex query and surfaces gate failure messages or shows the \"Generate PRD\" button when mode === \"generation\".\n• Remove/deprecate any prior multi-agent logic, ensuring backward compatibility by routing old entry points to the new cascade.\n", "testStrategy": "Unit-test each gate with representative PASS and FAIL inputs using Convex test harness. Verify early-exit short-circuiting by injecting failing input at each gate and asserting downstream gates are skipped. Test refinement loop for Specificity Gate with 0–3 iterations and ensure mode remains \"clarification\" until PASS. Integration tests: send complete conversation flows and confirm (a) state transitions, (b) correct failure messages, (c) final PRD generation includes earlier clarified details. Regression tests ensure legacy endpoints now call mainCascadeFlow without breaking existing functionality.", "subtasks": [{"id": 1, "title": "Define GateResult types and shared interfaces", "description": "", "status": "done", "dependencies": [], "details": "<info added on 2025-07-27T18:50:23.923Z>\nPort the skeleton workflow code into the production Convex tree and align it with the new GateResult interfaces:\n\n• Copy docs/convex/agents/workflows/example/cascade-workflow.ts → convex/workflows/cascadeWorkflow.ts  \n• Copy gate files (vaguenessGate.ts, focusGate.ts, scopeGate.ts, refinementGate.ts, responseGen.ts) → convex/workflows/gates/\n\nAdaptation checklist  \n1. Replace placeholder imports with shared interfaces defined in this subtask (`GateResult`, `GateContext`, `GateFailureReason`).  \n2. Convert each gate file to export a Convex action (`export const vaguenessGate = action({...})`) or query as appropriate; remove any OpenAI-specific stubs.  \n3. Update import paths to use the project alias/relative paths (`import { GateResult } from \"convex/workflows/sharedTypes\"`).  \n4. Refactor database interactions to use Convex `db` from the action context; strip out mock storage layers.  \n5. Review schema.ts – add/extend tables or enums referenced by the gates (e.g., `prd_ideas`, `clarifications`, `GateStatusEnum`).  \n6. Ensure cascadeWorkflow sequentially awaits each gate and short-circuits on first failure; propagate GateResult back to caller.  \n7. Delete redundant helper utilities already implemented elsewhere (e.g., duplicate zod validators).  \n8. Add missing dev/runtime deps (zod, lodash) to package.json and run `pnpm install`.  \n9. Run `pnpm type-check && pnpm test` to confirm no type or unit-test regressions; write a minimal vitest suite proving pass/fail flows compile.\n</info added on 2025-07-27T18:50:23.923Z>\n<info added on 2025-07-27T20:27:21.487Z>\nProgress update – core implementation completed:\n\n• Added convex/workflows/sharedTypes.ts with comprehensive type definitions (GateResult, DetailedGateResult, GateContext, GateFailureReason, CascadeFlowResult) and associated validators/config constants.  \n• Ported cascadeWorkflow orchestrator plus individual gate functions (vaguenessGate, focusGate, scopeGate, specificityGate) into convex/workflows/gates/, removing OpenAI-specific stubs and adapting to Convex action context.  \n• Introduced gate_results table and indexes in schema.ts for persisting execution outcomes.  \n• Replaced workflow mutations with direct function calls inside the cascade flow, preserving error handling and database writes.  \n• Implemented regex/heuristic detection logic with configurable confidence thresholds and detailed failure categorization per gate.  \n• Codebase compiles cleanly, passes linting, and is ready for integration tests.\n</info added on 2025-07-27T20:27:21.487Z>", "testStrategy": ""}, {"id": 2, "title": "Implement Vagueness Gate Convex function", "description": "", "status": "done", "dependencies": [], "details": "<info added on 2025-07-27T18:51:36.563Z>\nImplementation Details (updated)\n\n1. File structure  \n   • Rename `vagueness-gate-agent.ts` ➝ `convex/gates/vaguenessGate.ts` and convert to a Convex `mutation` so it can both read and persist data.  \n   • Export signature:  \n     ```\n     export const vaguenessGate = mutation({\n       args: { conversationId: v.id(\"conversations\"), message: v.string() },\n       handler: async (ctx, { conversationId, message }) => { … }\n     });\n     ```\n\n2. Detection logic refinements  \n   Replace the brittle keyword list with pattern buckets that mirror the PRD analysis rubric:  \n   a. Technical jargon / acronyms without context  \n      – Regex: `\\b(API|SDK|ML|AI|OCR|ETL|LLM|SaaS)\\b(?![^()]{0,40}\\()`  \n   b. Solution-first language (immediately proposes features without stating a problem)  \n      – Heuristic: sentence starts with a verb phrase (“Build”, “Create”, “Develop”, “Implement”) before any “problem” synonyms (“pain”, “issue”, “challenge”, “struggle”) appear.  \n   c. Architecture buzzwords without accompanying problem statement  \n      – Regex match on `\\b(microservice|serverless|event[- ]driven|dockerized|Kubernetes)\\b` with same problem-term distance check as above.  \n   For each match store `{patternId, snippet}` in `failReasons`.\n\n3. Convex schema integration  \n   • Extend `gate_results` table:  \n     ```\n     id: v.id(\"gate_results\")\n     conversationId: v.id(\"conversations\")\n     gate: v.literal(\"vagueness\")\n     status: v.union(v.literal(\"pass\"), v.literal(\"fail\"))\n     reasons: v.optional(v.array(v.string()))\n     createdAt: v.number()\n     ```  \n   • On mutation success write a row; on DB error throw `new Error(\"DB_WRITE_FAILED\")` to be caught by the pipeline runner.\n\n4. Response contract  \n   ```\n   type VaguenessGateResult =\n     | { status: \"pass\" }\n     | { status: \"fail\"; followUp: string; reasons: string[] };\n   ```  \n   • Generate `followUp` using `lib/prompts.generateClarification(reasons)` which wraps reasons in a single ask-for-clarity sentence.  \n   • Upstream caller short-circuits when `status === \"fail\"`.\n\n5. Error handling  \n   • Wrap pattern evaluation and DB write in try/catch; return `{ status:\"fail\", followUp:\"Internal error. Please try again.\" }` if unexpected exceptions occur, while logging the stack with `ctx.log.error`.  \n   • Distinguish between user-facing failures (vagueness) and system errors (500).\n\n6. Tests  \n   • Unit tests in `convex/gates/__tests__/vaguenessGate.test.ts` covering:  \n     – PASS: “Users forget passwords too often…” (no jargon).  \n     – FAIL-jargon: “We need an AI SaaS that uses LLMs…”  \n     – FAIL-solution: “Build a dashboard that shows metrics…”  \n     – DB write failure simulation with `jest.spyOn(ctx.db, \"insert\").mockRejectedValue(...)`.\n\nDeliverables  \n• Updated TypeScript file, schema patch, unit tests passing.\n</info added on 2025-07-27T18:51:36.563Z>", "testStrategy": ""}, {"id": 3, "title": "Implement Focus Gate Convex function", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:53:40.706Z>\n• Refactor `focus-gate-agent.ts` into a Convex server function `/convex/gates/focusGate.ts`, exporting `v1/focus_gate` that receives `{text: string}` and returns `{pass: boolean, msg?: string, clusters?: string[]}`.  \n• Port existing regex heuristics and expand them to production-grade multi-product detection:  \n  – Identify ≥2 distinct noun phrases preceded by capitalisation, commas, “and”, “or”, “plus”, “/”.  \n  – Detect pattern `<platform> AND <tool>` or `<mobile|web> app + <browser extension|AI assistant>` indicating composite products.  \n  – Flag explicit enumerations (bullet-like lists, semicolon separated items).  \n• Integrate with Convex schema `gate_logs` (already used by other gates): insert log row `{gate:\"focus\", text, pass, reason}`.  \n• On `pass === false` build a binary forcing response:  \n  1. Extract top two product clusters (via simple noun-phrase frequency).  \n  2. Return `msg` like: “Your request mentions both ‘{clusterA}’ and ‘{clusterB}’. Which single product would you like to focus on? Reply ‘A’ for {clusterA} or ‘B’ for {clusterB}.” and include `clusters:[\"A\",\"B\"]`.  \n• Update type definitions in `/convex/schema.ts` and `types/gates.ts`.  \n• Add unit tests in `/test/focusGate.test.ts` covering: single product (PASS), multi product with “and”, comma list, platform+tool combo, bullet list, and edge cases with conjunctions inside a single idea.  \n• Ensure gate short-circuits: export `focusGate()` and compose in the pipeline so downstream gates only run when `pass === true`.\n</info added on 2025-07-27T18:53:40.706Z>\n<info added on 2025-07-27T22:02:23.175Z>\n• Scale back testing scope: create a minimal sanity suite at `convex/workflows/gates/__tests__/focusGate.test.ts` with only 2–3 assertions—(1) single-product input returns `{pass:true}`, (2) multi-product input containing “and” returns `{pass:false}`, and (3) one representative edge case validating heuristics; comprehensive coverage will be provided later in subtask 5.9.\n</info added on 2025-07-27T22:02:23.175Z>", "testStrategy": ""}, {"id": 4, "title": "Implement Scope Gate Convex function", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:54:15.242Z>\nImplementation requirements for production-ready Scope Gate:\n\n• Replace scope-gate-agent.ts proof-of-concept with a Convex action/function (`scopeGate.ts`) that:\n  – Accepts the parsed PRD analysis object and user prompt as input.\n  – Flags “scope creep” when any of the following rules are true:\n      1. features.length > 5\n      2. >1 distinct core_function found in analysis.coreFunctions array\n      3. prompt or analysis.summary contains ecosystem-scale cues (e.g. “platform”, “marketplace”, “app store”, “plugin system”, “API for others”, “ecosystem”)\n\n• Persist evaluation result to `prd_gate_results` table (schema already used by Vagueness & Focus gates) with fields:\n  id, prdId, gate: \"scope\", passed: boolean, reason: string[], createdAt.\n\n• On FAIL, return an assistant response generated via <PERSON> with the template:\n  “Your current idea risks becoming a kitchen-sink product because: {{reasons}}.\n   Let’s narrow to a Minimal Lovable Product. Pick ONE primary user scenario and up to 3 must-have features.”\n\n• Ensure the function short-circuits the downstream pipeline by throwing `GateFailError` identical to other gates.\n\n• Unit tests:\n  – PASS example: 3 features, single core function, no ecosystem wording.\n  – FAIL examples for each rule above, verify stored reasons array and response copy.\n  – Integration test confirms pipeline halts and Specificity gate is skipped on FAIL.\n\n• Performance: Max execution 300 ms, memory < 20 MB; move all regex/string checks in-memory, no external calls except Claude on failure.\n\n• Update documentation in `/docs/gates.md` with rule set and response template.\n</info added on 2025-07-27T18:54:15.242Z>\n<info added on 2025-07-27T22:02:42.290Z>\nRevised testing scope:\n\n• Replace the previous exhaustive test suite with a lightweight smoke test file at `convex/workflows/gates/__tests__/clarityGate.test.ts`.\n• Implement only 2–3 assertions:\n  – Clear, well-formed input → expect PASS and `passed === true`.\n  – Unclear or ambiguous input → expect FAIL, populated `reason` array, and thrown `GateFailError`.\n  – One edge case (e.g., empty `features` array but ecosystem wording present) to confirm rule evaluation accuracy.\n• No integration tests or rule-by-rule permutations are required here; comprehensive coverage will be delivered in subtask 5.9.\n</info added on 2025-07-27T22:02:42.290Z>", "testStrategy": ""}, {"id": 5, "title": "Implement Specificity Check with refinement loop", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:54:50.534Z>\nImplementation details  \n• Refactor /lib/agents/refinement-agent.ts → /convex/gates/specificityGate.ts and swap the OpenAI call for the existing Claude 3.5 helper in /lib/llm.ts.  \n• Gate signature  \n  export const specificityGate = mutation(({db}, {msg, iteration}:{msg:string,iteration:number}) => Promise<GateResult>)  \n  where GateResult = {status:'PASS'|'ASK'|'LOCK_FAIL', question?:string, iteration:number}.  \n• Specificity heuristic (all must be satisfied to PASS)  \n  1. Explicit target user/persona (regex `(for|to)\\s+(?:busy|early|solo|small|enterprise|[a-z]+)\\s+(?:founders?|marketers?|developers?)`).  \n  2. Singular core feature statement detected by “the core feature is” or one bullet.  \n  3. Measurable success metric (contains % / $, “KPI”, “metric”, or quantifier).  \n  4. Problem statement < 160 chars starting with verb (“struggle”, “need”, “cannot”).  \n• On failure the agent returns status ASK and a single clarification question chosen from a question bank keyed to the missing item.  \n• mainCascadeFlow will re-invoke the gate until status PASS or iteration === 3.  \n• If iteration hits 3 without PASS return status LOCK_FAIL so downstream logic can either halt or fall back to manual triage.  \n• Store iteration count and lastQuestion in “refinement_sessions” table keyed by chatId to persist across requests.  \n• Maximum runtime 4s; set Claude temperature 0.3 for determinism.  \n• Unit-test three trajectories: immediate PASS, PASS on 2nd iteration, LOCK_FAIL after 3.\n</info added on 2025-07-27T18:54:50.534Z>\n<info added on 2025-07-27T22:03:02.331Z>\nRevised test scope  \n• Supersede the earlier “three-trajectory” plan with a lightweight smoke-test suite.  \n• Add `convex/workflows/gates/__tests__/specificityGate.test.ts` containing 2-3 cases only:  \n  – happy-path input that satisfies all heuristics → expect status ‘PASS’.  \n  – generic / vague input that misses at least one heuristic → expect status ‘ASK’.  \n  – one edge case (e.g., empty string or missing iteration) → expect graceful ‘LOCK_FAIL’ or typed error.  \n• Purpose is dependency verification and basic correctness; exhaustive scenarios will be implemented in task 5.9.\n</info added on 2025-07-27T22:03:02.331Z>", "testStrategy": ""}, {"id": 6, "title": "Create mainCascadeFlow mutation orchestrating gates with early exit", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:55:19.282Z>\nObjective:\nTurn the experimental cascade-workflow.ts prototype into a production-ready Convex mutation named mainCascade<PERSON><PERSON> that drives the four validation gates, performs short-circuiting on first failure, and persists all results to the conversation state tables.\n\nImplementation steps:\n1. File location: /convex/mainCascadeFlow.ts (export default async function mainCascadeFlow(ctx, {conversationId, userMessage}: {conversationId: Id<\"conversations\">; userMessage: string}))\n2. Import individual gate helpers (vaguenessGate, focusGate, scopeGate, specificityGate) from /convex/gates/.\n3. Retrieve current conversation object and prior messages with ctx.db.query(\"messages\").withIndex(\"by_conversation\", conversationId)… .\n4. Build a gatePipeline array of functions in execution order; iterate with for-of:\n   a. Call gate(userMessage, priorMessages).\n   b. If result.status === \"fail\", write a new assistant message containing result.clarificationPrompt, update conversation.mode to \"clarification\", and return {mode: \"clarification\"} early.\n5. If all gates pass, set conversation.mode = \"prdGeneration\", append a system message “All gates passed – proceeding to PRD generation”, and return {mode: \"prdGeneration\"}.\n6. Ensure atomicity with ctx.db.patch / ctx.db.insert inside a single mutation.\n7. Remove legacy multi-agent flow references; mark old fields deprecated.\n8. Export types GateResult, GateStatus for downstream consumers (UI and tests).\n\nTesting / acceptance criteria:\n• Unit tests using convex/dev harness feed representative inputs that fail each gate; assert mainCascadeFlow stops exactly at first failure and writes only one assistant clarification message.\n• Happy-path test where all gates pass verifies conversation.mode flips to \"prdGeneration\" and no extra messages are written.\n• Regression test confirms conversation state remains consistent after concurrent requests (use convex testing’s ctx.scheduler.runConcurrent).\n• Lint, type-check, and ensure 100% branch coverage on gate branching logic.\n</info added on 2025-07-27T18:55:19.282Z>\n<info added on 2025-07-27T22:03:23.474Z>\nReplace the previous comprehensive gate-coverage requirements with the following minimal testing scope:\n\n• Add a single smoke-test file at `convex/workflows/gates/__tests__/feasibilityGate.test.ts` containing 2–3 basic assertions: one “feasible” input that returns PASS, one “infeasible” input that returns FAIL, and one edge-case input to confirm correct status handling. These tests only verify correct imports and expected GateResult values; exhaustive scenarios and branch coverage will be delivered in task 5.9.\n</info added on 2025-07-27T22:03:23.474Z>", "testStrategy": ""}, {"id": 7, "title": "Integrate cascade flow into existing conversation state management and remove old multi-agent logic", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:55:56.383Z>\nImplementation outline for cascade integration and cleanup:\n\n• Schema update: add fields to `conversations` table –  \n  • `gateStates?: { [gate: string]: 'PASS' | 'FAIL' | 'PENDING' }`  \n  • `cascadeVersion?: number` (default 1) – with migration script that back-fills existing records with empty `gateStates` and version 1.\n\n• State helpers: create `setGateState(convId, gate, state)` and `getGateStates(convId)` utilities in `/convex/flows/cascadeState.ts` for consistent read/write access across mutations.\n\n• Refactor logic: replace all imports of `multiAgentController` with `executeCascadeFlow` inside `mainCascadeFlow` mutation; remove `agentPromptBuilder`, `agentTaskQueue`, and any `Agent*` types. Delete `agents/` directory and purge dead code from barrel exports.\n\n• Route adaptation: in `/app/api/chat/route.ts` append latest `gateStates` snapshot to the assistant message’s `metadata` field. Maintain existing message shape so the chat UI renders unchanged when `metadata.gateStates` is undefined.\n\n• Client compatibility: update `useChat` hook mapper to ignore unknown `metadata` keys, and guard new gate UI components behind `if (metadata?.gateStates)`.\n\n• Test migration:  \n  1. Port `multiAgent` unit tests to `cascade` equivalents with pass/fail fixtures.  \n  2. Add regression test ensuring old conversations (no `gateStates`) still render and accept messages.  \n\n• Documentation: update `/docs/architecture.md` and public types (`/types/chat.ts`) to describe hierarchical cascade system and new schema fields.\n\nDeliverables: schema migration, refactored mutations, removed agent code, updated API route & client guards, passing test suite, docs.\n</info added on 2025-07-27T18:55:56.383Z>\n<info added on 2025-07-27T22:03:53.304Z>\nRevise the testing scope for this subtask:\n\n• Replace the previous “Test migration” bullet with a lightweight smoke-test requirement.  \n• Create a single test file `convex/workflows/gates/__tests__/mainCascadeFlow.test.ts` containing only 2–3 basic cases:  \n  1. All gates PASS → success flow reaches the end.  \n  2. First gate FAILS → early exit with no downstream gate execution.  \n  3. One simple edge case (e.g., empty user input) verifying graceful handling.  \n• Purpose is limited to ensuring correct imports, cascade orchestration, and state mutation wiring; comprehensive coverage and legacy-conversation regressions are deferred to Task 5.9.  \n• Remove plans to port the full multi-agent test suite in this subtask.\n</info added on 2025-07-27T22:03:53.304Z>", "testStrategy": ""}, {"id": 8, "title": "Update client-side components to display gate feedback and show 'Generate PRD' button on PASS", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:56:40.871Z>\nImplementation details:\n• Create a shared Convex table `gate_runs` keyed by conversation id that stores current gate index, status (pending | pass | fail), and failureMessage.  \n• Expose a `listGateStates` query returning ordered gate objects and a `currentMode` field (“clarification” | “generation”).  \n• Build three new client-side React components in `/components/cascade`:\n  1. `GateProgressBar.tsx` – horizontal stepper that renders the four gates with icons/colors for pending (grey), pass (green) and fail (red). Consumes `useQuery(api.gates.listGateStates, {conversationId})`.  \n  2. `GateFeedback.tsx` – conditional alert shown below the chat input when the latest gate status === “fail”. Renders `failureMessage` and CTA text like “Please clarify and resend”.  \n  3. `GeneratePrdButton.tsx` – primary button surfaced when `currentMode === \"generation\"` AND every gate state === “pass”. On click it posts a “Generate PRD” assistant trigger message to the chat stream.\n\nUI changes:\n• Mount `GateProgressBar` at top of `/chat/page.tsx` under the conversation header.  \n• Mount `GateFeedback` just above the message list so failures are immediately visible.  \n• Place `GeneratePrdButton` within the chat input toolbar; hide it in clarification mode.\n\nUX rules:\n• While a gate is evaluating, show a small indeterminate progress indicator beside that gate’s name.  \n• On any failure, freeze subsequent gates to “pending” until the user sends another message.  \n• Auto-scroll to the bottom when the button injects the “Generate PRD” command.\n\nAcceptance criteria:\n1. Progress bar updates in real time without manual refresh.  \n2. Correct failure message displayed for each gate’s specific rejection.  \n3. Button only appears after all four gates pass and disappears if a later user message fails a gate.  \n4. Clicking button triggers mode switch and server receives “generate_prd” command.\n\nTest strategy:\n• Mock gate states in Storybook to snapshot PASS, FAIL, and pending states.  \n• Cypress e2e: send messages that intentionally fail each gate and assert UI responses.  \n• Unit test `GeneratePrdButton` logic to ensure correct visibility based on query values.\n</info added on 2025-07-27T18:56:40.871Z>\n<info added on 2025-07-27T22:04:15.616Z>\nTesting scope adjustment:\n• Implement a lightweight test file `convex/workflows/gates/__tests__/cascadeIntegration.test.ts` with 2–3 assertions that verify (1) a complete PASS flow, (2) error handling when a gate rejects, and (3) one edge condition such as an unexpected `undefined` gate state. These smoke tests exist solely to confirm wiring and imports; exhaustive scenarios will be covered in subtask 5.9.\n</info added on 2025-07-27T22:04:15.616Z>", "testStrategy": ""}, {"id": 9, "title": "Write unit tests for individual gate functions", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:57:55.050Z>\nWrite a full Convex test suite that exercises every gate function in isolation:\n\n1. Test scenarios  \n   • PASS-case: supply at least three representative inputs per gate that should succeed and confirm the function returns `{status: 'PASS', message: expect.any(String)}`.  \n   • FAIL-case: supply at least three representative inputs per gate that should fail and confirm returned clarifying message matches the gate’s specification.  \n   • Edge/boundary inputs: empty string, extremely long strings (>10 k chars), malformed JSON payloads, and repeated whitespace.  \n   • Database interaction: mock `db` object to assert that gate feedback is written with correct `gateName`, `status`, and `userId` for both PASS and FAIL paths; verify no extraneous writes occur.  \n   • Error handling: force underlying helper utilities to throw and assert the gate returns `{status:'ERROR'}` and logs an exception event.  \n\n2. Framework integration  \n   • Use `convex test` with the built-in Jest environment; no external test runner.  \n   • Place test files under `tests/gates/*.test.ts`.  \n   • Import gates via `import {vaguenessGate, focusGate, scopeGate, specificityGate} from '../../../convex/gates';` and inject a mocked `actionCtx`.  \n   • Leverage Convex’s `internalMutation` mock helpers to spy on database writes.\n\n3. Coverage targets  \n   • Achieve ≥95 % statement and branch coverage across `convex/gates.ts`.  \n   • Include coverage thresholds in `package.json` test script; CI must fail if unmet.\n\n4. Deliverables  \n   • Test files committed and passing with `pnpm test`.  \n   • Updated README section “Gate Testing” describing how to run and interpret the suite.\n</info added on 2025-07-27T18:57:55.050Z>", "testStrategy": ""}, {"id": 10, "title": "Write integration tests for full cascade and state transitions", "description": "", "status": "pending", "dependencies": [], "details": "<info added on 2025-07-27T18:58:21.354Z>\nCreate a comprehensive integration-test suite that drives the entire gate cascade from the chat entry point and asserts end-to-end behaviour.\n\nScope\n• Exercise full user journeys that (a) succeed through all four gates, (b) fail at each individual gate, and (c) loop through the Specificity refinement cycle 0-3 times before succeeding.  \n• Assert state flags switch correctly between “clarification” and “generation” modes on:  \n  – first successful pass of all gates after a “Generate PRD” command  \n  – fallback to clarification when a new ambiguous message arrives after PRD output.  \n• Verify that when a gate fails, downstream gates are skipped and the correct clarifying question is returned to the chat UI.  \n• Inject artificial errors (e.g. thrown exception inside a gate) and confirm graceful error message, no state corruption, and ability to continue the conversation afterward.  \n• Confirm integration with the existing chat interface: messages appear in proper order, streaming works, and assistant role tags are preserved.  \n• Validate that rate-limiting headers (from Task 7) and other shared middleware are not broken by the cascade.\n\nImplementation Notes\n• Use Convex test harness for server functions plus Playwright component tests for the `/app/chat` page to capture real UI output.  \n• Provide helper fixtures that build mock conversation transcripts and assert final Convex mutation results plus rendered DOM text.  \n• Aim for ≥90 % branch coverage across `cascade.ts`, gate functions, and chat API route.\n\nAcceptance Criteria\nAll tests pass in CI, coverage thresholds are met, and failures in any part of the cascade (logic or UI) are surfaced with clear assertions.\n</info added on 2025-07-27T18:58:21.354Z>", "testStrategy": ""}]}, {"id": 6, "title": "Create PRD Preview Component", "description": "Build markdown preview panel for real-time PRD generation with copy and download functionality, enhanced with Convex real-time subscriptions for improved streaming performance", "status": "pending", "dependencies": ["5"], "priority": "medium", "details": "Create `/components/prd-preview.tsx` using Tailwind Typography for markdown rendering. Implement split-screen layout with chat on left and PRD preview on right. Add real-time streaming preview using Convex subscriptions for better performance and optimistic UI updates. Include copy to clipboard and download as .md file functionality. Make preview responsive - collapse to tabs on mobile. Add markdown parsing and syntax highlighting for code blocks. Focus on single-user experience with seamless real-time updates.", "testStrategy": "Test markdown rendering with various PRD formats, verify copy/download functions work correctly, validate responsive behavior on mobile devices, ensure Convex real-time subscriptions work properly, and test optimistic UI updates during streaming", "subtasks": [{"id": 1, "title": "Setup Basic Markdown Rendering", "description": "Create /components/prd-preview.tsx with @tailwindcss/typography for markdown display", "status": "pending", "dependencies": [], "details": "Install @tailwindcss/typography, create basic component structure, implement markdown parsing with proper styling, test with sample content", "testStrategy": ""}, {"id": 2, "title": "Implement Split-Screen Layout", "description": "Build responsive layout with chat on left and preview on right", "status": "pending", "dependencies": [], "details": "Use CSS Grid/Flexbox for desktop split-screen, implement mobile-responsive tabs, handle window resizing and breakpoints", "testStrategy": ""}, {"id": 3, "title": "Add Convex Real-Time Subscriptions", "description": "Integrate Convex real-time subscriptions for improved streaming performance", "status": "pending", "dependencies": [], "details": "Set up Convex queries/mutations for PRD data, implement real-time subscriptions for live updates, replace direct chat streaming with Convex-based updates, add error handling for connection issues", "testStrategy": ""}, {"id": 4, "title": "Implement Optimistic UI Updates", "description": "Add optimistic updates for better single-user experience during PRD generation", "status": "pending", "dependencies": [3], "details": "Implement optimistic markdown rendering before Convex confirmation, add loading indicators and smooth transitions, handle rollback scenarios for failed updates, ensure UI remains responsive during generation", "testStrategy": ""}, {"id": 5, "title": "Implement Copy & Download Functionality", "description": "Add copy to clipboard and download as .md file features", "status": "pending", "dependencies": [], "details": "Create copy button with success feedback, implement markdown file download with proper filename, add toast notifications, handle browser compatibility", "testStrategy": ""}, {"id": 6, "title": "Add Mobile Responsive Behavior", "description": "Optimize preview component for mobile devices", "status": "pending", "dependencies": [], "details": "Implement tab-based switching on mobile, optimize touch interactions, ensure readability on small screens, test download on mobile browsers", "testStrategy": ""}, {"id": 7, "title": "Polish Preview & Handle Edge Cases", "description": "Add syntax highlighting and error handling for robust preview with Convex integration", "status": "pending", "dependencies": [3], "details": "Implement code block syntax highlighting, handle malformed markdown, add loading states during Convex operations, create empty state UI, handle Convex connection failures gracefully", "testStrategy": ""}]}, {"id": 7, "title": "Implement Rate Limiting", "description": "Add IP-based rate limiting to prevent abuse with 10 PRDs per day limit for public MVP using @convex-dev/rate-limiter package", "status": "pending", "dependencies": ["5"], "priority": "medium", "details": "Implement rate limiting using the @convex-dev/rate-limiter package for IP tracking and request throttling. Install and configure the rate limiter package to track IP addresses and request counts with 10 PRD generations per 24-hour period. The package provides built-in Convex integration for storing rate limit data. Integrate rate limiting checks into existing API routes that generate PRDs. Create user-friendly error messages when limits are exceeded. Include rate limit headers in responses using the rate limiter's response utilities.", "testStrategy": "Test rate limiting with multiple requests from same IP using @convex-dev/rate-limiter, verify limits reset after 24 hours, validate error messages are user-friendly, confirm rate limit headers are included in API responses, and test rate limiter performance under load scenarios", "subtasks": []}, {"id": 8, "title": "Design Landing Page", "description": "Create compelling landing page with value proposition, example PRD showcase, and clear call-to-action", "details": "Design `/app/page.tsx` as static landing page with hero section explaining PRDGeneral's value proposition. Include example PRD or before/after transformation showcase. Add prominent 'Start Clarifying' CTA button leading to /chat. Implement responsive design with mobile-first approach. Include brief feature highlights: Claude-powered clarification, one-click PRD generation, clean markdown export. Use Tailwind CSS for styling with modern, clean design.", "testStrategy": "Test landing page load speed, verify responsive design across devices, validate CTA button functionality, and ensure clear value proposition communication", "priority": "medium", "dependencies": ["6"], "status": "pending", "subtasks": []}, {"id": 9, "title": "Add Error Handling and Loading States", "description": "Implement comprehensive error boundaries, loading states, and user feedback for all interactions using Convex's built-in features", "status": "pending", "dependencies": ["5", "6"], "priority": "medium", "details": "Create error boundaries for chat and PRD preview components with Convex error handling integration. Add loading spinners and skeleton states during message generation using Convex's loading states. Implement automatic retry logic for failed API calls using Convex's built-in retry mechanisms. Leverage Convex optimistic updates for better single-user experience and state consistency. Create user-friendly error messages for common issues (API errors, network problems, rate limits). Add `/app/chat/loading.tsx` for Next.js loading UI. Include toast notifications for successful actions (copy, download). Handle edge cases like empty responses or malformed markdown with Convex error boundaries.", "testStrategy": "Test error scenarios including network failures, API errors, and malformed responses with Convex error handling. Verify loading states appear correctly using Convex loading indicators and optimistic updates work as expected. Test automatic retry functionality and state consistency. Validate user feedback is clear and actionable.", "subtasks": []}, {"id": 10, "title": "Deploy to Vercel and Production Setup", "description": "Configure production deployment on Vercel with environment variables and monitoring", "details": "Connect GitHub repository to Vercel for automatic deployments. Configure environment variables in Vercel dashboard (ANTHROPIC_API_KEY). Set up custom domain if available. Configure Vercel Analytics for basic monitoring. Add production error logging with Vercel's built-in monitoring. Test production deployment thoroughly including API routes, static assets, and environment variable access. Ensure Edge Runtime compatibility if using edge functions.", "testStrategy": "Verify production deployment works correctly, test all features in production environment, validate environment variables are properly configured, and confirm error monitoring is functional", "priority": "high", "dependencies": ["7", "9"], "status": "pending", "subtasks": []}, {"id": 11, "title": "Convex Backend Migration", "description": "Migrate from Next.js API routes to Convex backend functions with real-time subscriptions, authentication, and TypeScript schema definitions", "status": "done", "dependencies": [2], "priority": "high", "details": "Install Convex SDK with `pnpm install convex` and run `pnpx convex dev` to initialize. Create `/convex` directory with schema definitions in `schema.ts` using Convex's TypeScript schema system. Migrate existing API routes to Convex functions in `/convex/functions/` directory. Set up Convex authentication system replacing current auth implementation. Convert chat API route to Convex mutation/query functions with real-time streaming capabilities. Implement real-time subscriptions for live PRD updates using Convex's reactive queries. Update client-side code to use `useConvexAuth()` and `useQuery()`/`useMutation()` hooks. Configure Convex environment variables and deploy configuration. Add TypeScript definitions for all data models including chat messages, PRD generations, and user sessions.", "testStrategy": "Verify Convex development environment starts correctly and syncs schema changes. Test migration of all existing API functionality to Convex functions. Validate real-time subscriptions work for live PRD updates. Confirm authentication flows work with Convex auth system. Test TypeScript compilation with new Convex schemas. Verify production deployment works with Convex backend. Load test real-time features with multiple concurrent users.", "subtasks": [{"id": 1, "title": "Design Convex Schema", "description": "Model all application data in Convex’s TypeScript schema system.", "dependencies": [], "details": "Audit existing Next.js API route models, map to Convex documents, define tables, indexes, and relationships in /convex/schema.ts. Include versioning fields for future migrations.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Set Up Authentication with Convex", "description": "Replace current auth implementation with Convex’s auth module.", "dependencies": ["11.1"], "details": "Configure Convex auth provider, session management, and role-based access rules; update environment variables and secure routes.\n<info added on 2025-07-22T19:32:47.923Z>\nAdd Google OAuth setup instructions:\n• In Google Cloud Console → APIs & Services → Credentials, create an OAuth 2.0 Client ID (application type: Web).  \n• Set Authorized redirect URI to https://industrious-egret-645.convex.site/api/auth/callback/google.  \n• Copy Client ID and Client Secret into project root .env as  \n  AUTH_GOOGLE_ID=<client_id>  \n  AUTH_GOOGLE_SECRET=<client_secret>  \n  (also add placeholders to .env.example).  \n• Restart `pnpx convex dev` (and Vercel preview/prod deployments) so Convex picks up the new vars.  \n• Verify login flow by authenticating with a Google account; ensure session object contains provider === 'google' and role assignment logic still applies.\n</info added on 2025-07-22T19:32:47.923Z>\n<info added on 2025-07-22T19:40:12.134Z>\n• In Google OAuth “Authorized JavaScript origins”, enter the base URL of your Convex deployment (no path or trailing slash), e.g. https://industrious-egret-645.convex.site.  \n• To locate this URL, run `npx convex dashboard` or open the Convex dashboard and copy the HTTPS address with the format https://<adjective-animal-###>.convex.site.  \n• JavaScript origins specify where your frontend initiates auth requests; they differ from redirect URIs (where Google sends the auth response). Origins cannot contain wildcards or paths and must include a port if it’s not 80/443.  \n• Save changes, then restart `pnpx convex dev` (and redeploy Vercel previews/prod) to apply the updated OAuth settings.\n</info added on 2025-07-22T19:40:12.134Z>\n<info added on 2025-07-22T19:57:00.630Z>\n• Persist the credentials to Convex’s server-side environment so auth functions can access them:  \n  npx convex env set AUTH_GOOGLE_ID <client_id>  \n  npx convex env set AUTH_GOOGLE_SECRET <client_secret>  \n  (substitute the actual values).  \n• Restart `pnpx convex dev` and redeploy previews/prod to load the new vars into your Convex functions.\n</info added on 2025-07-22T19:57:00.630Z>\n<info added on 2025-07-22T20:03:00.994Z>\nAdd GitHub OAuth setup instructions:  \n• In GitHub → Settings → Developer settings → OAuth Apps, click “New OAuth App”.  \n• Set Application name (e.g., “PRDGeneral”), Homepage URL to https://industrious-egret-645.convex.site, and Authorization callback URL to https://industrious-egret-645.convex.site/api/auth/callback/github.  \n• Register the app, then copy the Client ID and generate a new Client Secret.  \n• Persist the creds to Convex’s server-side environment (these are required even if they exist in a local .env):  \n  npx convex env set AUTH_GITHUB_ID <client_id>  \n  npx convex env set AUTH_GITHUB_SECRET <client_secret>  \n  (also add placeholders to .env.example for clarity).  \n• Restart `pnpx convex dev` and redeploy Vercel previews/prod so Convex functions load the new vars alongside the Google creds.  \n• Verify GitHub sign-in; ensure session.provider === 'github' and that role assignment logic functions identically to Google logins.\n</info added on 2025-07-22T20:03:00.994Z>\n<info added on 2025-07-22T20:29:10.352Z>\n• Implement automatic user record creation during authentication flow:  \n  1. Extend `convex/schema.ts` with a `users` table (if not already present) and add a secondary index on `providerId` to support quick look-ups:  \n     ```ts\n     users: defineTable({\n       provider: v.string(),\n       providerId: v.string(),   // e.g. sub / id from OAuth provider\n       name: v.optional(v.string()),\n       email: v.optional(v.string()),\n       avatarUrl: v.optional(v.string()),\n       role: v.string(),         // 'user' | 'admin'\n       createdAt: v.number(),\n     }).index(\"providerId\", [\"providerId\"]),\n     ```  \n  2. Replace the read-only `users.ts` query with an upsert-style mutation `users/createIfMissing.ts` that inserts a row when no match is found:  \n     ```ts\n     // /convex/functions/users/createIfMissing.ts\n     import { mutation } from \"./_generated/server\";\n     import { v } from \"convex/values\";\n\n     export default mutation({\n       args: {\n         provider: v.string(),\n         providerId: v.string(),\n         name: v.optional(v.string()),\n         email: v.optional(v.string()),\n         avatarUrl: v.optional(v.string()),\n       },\n       handler: async (ctx, args) => {\n         const existing = await ctx.db\n           .query(\"users\")\n           .withIndex(\"providerId\", q => q.eq(\"providerId\", args.providerId))\n           .unique();\n         if (existing) return existing._id;\n\n         return await ctx.db.insert(\"users\", {\n           ...args,\n           role: \"user\",\n           createdAt: Date.now(),\n         });\n       },\n     });\n     ```  \n  3. In the auth callback (e.g. `/convex/functions/auth/handleLogin.ts`), invoke the mutation immediately after obtaining the Convex session:  \n     ```ts\n     const { provider, providerId, name, email, avatarUrl } = sessionIdentity;\n     const userId = await ctx.runMutation(\"users/createIfMissing\", {\n       provider,\n       providerId,\n       name,\n       email,\n       avatarUrl,\n     });\n     session.userId = userId;   // attach for downstream use\n     ```  \n  4. Add unit test `users.test.ts` verifying that first-time logins insert a new record and repeat logins reuse the same `userId`.  \n  5. Update role-assignment logic to rely on `session.userId` rather than failing when the user table is empty.  \n\nAfter deploying, first-time Google or GitHub sign-ins will automatically create a `users` row, eliminating the current authentication failure when no existing record is found.\n</info added on 2025-07-22T20:29:10.352Z>", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Convert API Routes to Convex Functions", "description": "Rewrite existing Next.js API logic as Convex mutations and queries.", "dependencies": ["11.1", "11.2"], "details": "Create files in /convex/functions/, replicate business logic, ensure type safety, and remove obsolete API route files.\n<info added on 2025-07-22T20:31:18.625Z>\nAdd per-user/IP rate-limiting middleware to every new Convex mutation and query (token-bucket with configurable window; return 429 ConvexError when exceeded). Wrap all function bodies in a shared try/catch utility that emits structured logs containing functionName, userId/sessionId, input params, and stack trace to the chosen logger/Sentry before re-throwing typed ConvexError objects. When handling anonymous sessions, enforce uniqueness by first querying for existing sessionId; on collision, regenerate a cryptographically secure UUID v4 and retry until unique, then persist.\n</info added on 2025-07-22T20:31:18.625Z>", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Implement Real-Time Subscriptions", "description": "Enable live data streaming for critical features using Convex listeners.", "dependencies": ["11.3"], "details": "Set up onUpdate listeners for chat and PRD updates, test client-side reactivity, and handle throttling/back-pressure.\n<info added on 2025-07-22T20:33:06.107Z>\nComplete the streaming pipeline for real-time chat:\n\n• Refactor /convex/functions/chatCompletion.ts to return an async generator that yields <PERSON> token chunks and writes them to the messages table in near-real-time (`status: \"streaming\"`).  \n• Buffer chunks (e.g., 50–100 ms) to batch writes and stay within Convex 20 mutations/sec limit; flush on final chunk and mark `status: \"done\"`.  \n• Wrap streaming loop in try/catch; on error capture message, set `status: \"error\"`, persist `errorMessage`, and propagate failure to client.  \n• Add AbortController support: store controller in a `generationSessions` map keyed by messageId; expose /convex/functions/cancelGeneration mutation to allow UI “Stop” button to abort the stream.  \n• Implement connection resilience: on websocket reconnect, re-subscribe to the live query and request any missed chunks; resume stream only if `status === \"streaming\"`.  \n• Update client chat hook to concatenate incoming chunks, stop polling logic, surface `isStreaming`, `isError`, and `isCancelled` flags, and debounce re-renders to <16 ms.  \n• Integration tests: simulated chunked response verifies correct ordering and no duplicate/missing tokens; cancellation test ensures abort stops further writes within 200 ms; reconnect test validates automatic resubscribe restores stream without data loss.\n</info added on 2025-07-22T20:33:06.107Z>\n<info added on 2025-07-24T19:55:29.221Z>\nUpdate for “UI-first” streaming model:\n\n• Modify /convex/functions/chatCompletion.ts  \n  – Return an async generator that yields Claude chunks to the client immediately.  \n  – Accumulate chunks server-side in a local buffer; after the generator finishes (or aborts), commit exactly one record to messages with the full text and status \"done\" (or \"error\"/\"cancelled\").  \n  – Remove incremental message writes and the “streaming” status column; instead track in-flight sessions only in memory.\n\n• generationSessions & AbortController  \n  – Keep a map {messageId: {controller, buffer}}.  \n  – Expose cancelGeneration mutation; on cancel, abort controller, mark DB row “cancelled” if it already exists, else skip insert.\n\n• Client chat hook  \n  – openStream(messageId) starts the Convex action, appends incoming chunks to local streamedText state for live rendering.  \n  – Flags: isStreaming (action in flight), isError, canCancel (controller present & !done).  \n  – When live query returns the final stored row, replace streamedText with it and clear isStreaming.\n\n• Connection resilience  \n  – On websocket drop, preserve local streamedText; on reconnect, resume live query.  \n  – If the action is still running, streamedText continues when transport is re-established; otherwise the final DB record arrives.\n\n• Tests  \n  – chunkStreaming.test.ts: UI receives ordered, non-duplicated chunks within 50 ms average latency.  \n  – cancelGeneration.test.ts: abort stops further chunks ≤200 ms, no DB insert.  \n  – finalWrite.test.ts: stored message equals concatenated chunks.  \n  – reconnectResilience.test.ts: simulate disconnect; after reconnect UI resumes stream or swaps in final message without loss/duplication.\n</info added on 2025-07-24T19:55:29.221Z>\n<info added on 2025-07-25T21:31:33.850Z>\nIMPLEMENTATION COMPLETED – UI-First Streaming Architecture\n\n• Core pipeline (convex/chatCompletion.ts): async generator streams chunks directly to the client; server buffers full response and performs a single final insert (status: \"done\" | \"error\" | \"cancelled\").  \n• Session management (convex/lib/streamingSession.ts): in-memory AbortControllers with 5-min TTL, per-user concurrency limits, automatic cleanup, and `cancelGeneration` action for hard stops.  \n• Client hook (useStreamingChat.ts): real-time chunk rendering with `isStreaming`, `canCancel`, optimistic UI, and auto-resubscribe on websocket reconnect.  \n• Throttling/back-pressure: removal of per-chunk writes eliminates Convex 20 mut/s ceiling; session limits prevent abuse.  \n• Comprehensive tests (440+ LOC) cover streaming order, cancellation (<200 ms), TTL expiry, concurrency, reconnect resilience, and edge cases.  \n• Performance: <50 ms avg chunk latency, zero token loss/duplication.\n\nARCHITECTURAL RATIONALE\n\n1. UI-first streaming avoids mutation rate limits, reduces DB noise, and simplifies state.  \n2. Agent-based client design cleanly separates streaming logic from thread management and supports future multi-agent workflows.  \n3. Ephemeral session rows keep message records immutable while enabling accurate cancellation and observability.\n\nSubtask 11.4 is now COMPLETE; proceed to 11.5 “Update Client Adapters for Convex”.\n</info added on 2025-07-25T21:31:33.850Z>", "status": "done", "testStrategy": ""}, {"id": 5, "title": "Update Client Adapters for Convex", "description": "Refactor frontend hooks and services to consume Convex queries/mutations.", "dependencies": ["11.3", "11.4"], "details": "Integrate useQuery/useMutation hooks, remove REST fetches, and ensure TypeScript types align with backend schema.", "status": "done", "testStrategy": ""}]}, {"id": 12, "title": "Optimize & Harden Convex Backend for Production", "description": "Prepare the Convex backend for production by adding staging deployment, performance optimizations (pagination, indexing, load-test tuning), configuration management, CI/CD with rollback, and an exhaustive test suite.", "details": "1. Staging & Prod Environments\n   • Create two Convex deployments via `convex deploy staging` and `convex deploy prod`.\n   • In the Convex dashboard, add distinct environment variables (ANTHROPIC_API_KEY, RATE_LIMIT, FEATURE_FLAGS) per deployment.\n   • Annotate deployments with git SHA tags for traceability (`convex deploy --tag $GITHUB_SHA`).\n\n2. CI/CD & Rollback\n   • Add a GitHub Action workflow (`.github/workflows/convex.yml`) that:\n     a. Runs unit tests & lints Convex functions.\n     b. Deploys automatically to staging on every push to `main`.\n     c. Prompts for a manual approval job that promotes the current staging build to production (`convex deploy prod --tag $GITHUB_SHA`).\n   • Implement rollback script `scripts/convex-rollback.sh` that lists previous prod tags and redeploys (`convex deploy prod --tag TAG_TO_ROLLBACK`).\n\n3. Performance & Pagination\n   • Refactor heavy list queries (chat messages, PRD revisions) to use cursor-based pagination: `query({ after }) ⇒ paginate({ limit: 50 })`.\n   • Add composite indexes in `convex/schema.ts` on `[conversationId, createdAt]` and `[userId, createdAt]`.\n   • Use `internal` functions or batch mutations to reduce round-trips during streaming.\n   • Cache common readonly queries with `convex.actionCache(‘5s’)` where appropriate.\n\n4. Load Testing\n   • Write Artillery scenario (`load.yml`) that simulates 100 concurrent users generating PRDs (clarification + generation cycles).\n   • Stage run via `artillery run load.yml --target https://staging.prdgeneral.ai` and capture p95 latency, error rate.\n   • Tune Convex deployment scale limits (compute + database) until p95 < 300 ms and error rate < 1 % at 100 RPS sustained.\n\n5. Monitoring & Alerts\n   • Enable Convex Observability dashboards and set alerts for error rate > 2 % or latency > 400 ms (Slack webhook).\n   • Expose custom metric `prd_generated_total` via Convex logging and verify it appears in dashboard.\n\n6. Comprehensive Test Suite\n   • Unit tests: use `vitest` + `@convex/testing` for function-level tests (rate-limited mutation, paginated query returns expected cursors).\n   • Integration tests: Playwright tests that walk through full clarification → generation flow hitting staging.\n   • Smoke test script `npm run smoke:prod` executed post-deployment by CI.\n\n7. Documentation\n   • Add `docs/operating-convex.md` covering promotion, rollback, env variables, and disaster recovery.\n", "testStrategy": "• Environment Verification: Run `convex deploy staging` and confirm separate URL & variables; promote to prod and check tag.\n• CI Pipeline: Push dummy commit; ensure GitHub Action runs tests, deploys to staging, awaits manual approval, then deploys to prod.\n• Rollback Drill: Deploy a failing commit to staging, promote to prod, run rollback script, and verify previous version restored.\n• Load Test Pass Criteria: Execute Artillery load test; assert p95 latency < 300 ms and error rate < 1 %.\n• Pagination Tests: Insert 120 messages, query with limit 50—assert three pages returned, cursors valid, no duplicate/ missing items.\n• Index Effectiveness: Use Convex dashboard query profiler—confirm indexed query drops from >800 ms to <50 ms.\n• End-to-End Tests: Playwright script generates PRD on staging; assert success status and correct markdown structure.\n• Monitoring & Alerts: Manually trigger function error; confirm alert delivered to Slack within 1 minute.\n", "status": "pending", "dependencies": [11], "priority": "medium", "subtasks": [{"id": 1, "title": "Deploy to Staging & Verification", "description": "<PERSON><PERSON> migrated backend to staging environment and run end-to-end tests.", "dependencies": ["11.5"], "details": "Automate deployment with CI, seed test data, validate auth, data integrity, and real-time flows across devices.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Perform Load & Performance Testing", "description": "Stress-test Convex backend under expected and peak loads.", "dependencies": ["10", "11.6"], "details": "Use k6 or Artillery to simulate concurrent users, monitor latency, throughput, and resource usage, and optimize indexes/functions as needed.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Create Rollback & Contingency Plan", "description": "Document and automate safe rollback procedures in case of production issues.", "dependencies": ["10", "11.6", "11.7"], "details": "Define data export/import scripts, versioned deployment tags, traffic switching steps, and communication plan with stakeholders.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Add Pagination and Performance Features", "description": "Implement pagination for message queries and add performance monitoring capabilities", "details": "Add pagination support to message queries to handle large chat histories efficiently. Implement performance monitoring with timing and usage metrics collection. Add caching for mode detection results to improve performance.", "status": "pending", "dependencies": ["10", "11.3"], "parentTaskId": 11}, {"id": 5, "title": "Configuration Management and Code Quality", "description": "Externalize system prompts and refactor hardcoded values to configuration", "details": "Move system prompts from code to configuration files or database for easier management. Extract hardcoded thresholds and magic numbers to configuration files. Add enhanced access control with IP-based restrictions for anonymous users.", "status": "pending", "dependencies": ["10", "11.3"], "parentTaskId": 11}, {"id": 6, "title": "Comprehensive Testing Suite", "description": "Create unit tests for mode detection, validation logic, and critical bug fixes", "details": "Implement unit tests for mode detection and validation logic. Add tests for user auto-creation functionality. Create tests for session ID uniqueness validation. Add integration tests for rate limiting and error handling.", "status": "pending", "dependencies": ["10", "11.2", "11.3"], "parentTaskId": 11}]}], "metadata": {"created": "2025-07-18T17:31:16.779Z", "updated": "2025-07-27T22:05:04.784Z", "description": "Tasks for master context"}}, "nice-to-have": {"tasks": [{"id": 1, "title": "Implement Advanced PRD Intent Classification Enhancements", "description": "Add non-MVP modules—including a Problem-Solution Pattern Matcher, Product Type Classifier, and 4-Phase Chain of Verification (CoVe)—to enrich PRD intent understanding and handle edge cases.", "details": "1. Architectural Placement\n   • Extend the existing IntentClassifier service with a plug-in style interface (e.g., Strategy pattern) so advanced modules can be toggled on/off via a feature flag (ENV var ADVANCED_PRD=true).\n\n2. Problem-Solution Pattern Matcher\n   • Curate a dataset of PRD excerpts annotated with the canonical “problem → solution → benefit” structure.\n   • Fine-tune a transformer (e.g., DeBERTa-v3) to output BEGIN/INSIDE/OUTSIDE tags for each span.\n   • Post-process tagged spans into a JSON section {\"problem\":..., \"solution\":..., \"benefit\":...}.\n   • Expose a /patterns endpoint that, given raw PRD text, returns detected structures and confidence scores.\n\n3. Product Type Classifier Module\n   • Multi-label classifier that predicts B2B, B2C, Mobile, Web, SaaS, Hardware, etc.\n   • Use the same text encoder as the core model to minimise latency; add a sigmoid output layer with binary-cross-entropy loss.\n   • Provide class weights to counter data imbalance and enable threshold tuning per label.\n\n4. 4-Phase Chain of Verification (CoVe)\n   • Phase 1: Syntactic validation – ensure required sections (overview, users, metrics) exist via regex+heuristics.\n   • Phase 2: Semantic sanity – run predictions twice with dropout (MC Dropout) and flag if confidence variance > Θ.\n   • Phase 3: Cross-module consensus – if core classifier and pattern matcher disagree, mark as \"review_needed\".\n   • Phase 4: External rules engine – apply domain-specific rules (e.g., security requirements for B2B SaaS).\n   • Implement CoVe as an asynchronous pipeline that annotates PRD objects with {\"status\":\"ok\"|\"warning\"|\"error\", \"reasons\":[...]}.\n\n5. Integration\n   • Update API documentation to include new endpoints and JSON schemas.\n   • Add protobuf definitions if gRPC is used.\n   • Ensure backward compatibility: existing clients receive identical responses when the ADVANCED_PRD flag is false.\n\n6. Performance & Ops\n   • Target <150 ms additional latency at P95.\n   • Export Prometheus metrics: pattern_matcher_latency_ms, product_type_accuracy_rolling, cove_disagreements_total.\n   • Provide Kubernetes Helm values to toggle individual sub-modules.", "testStrategy": "Unit Tests\n• Each module has ≥90 % code coverage.\n• Snapshot tests verify JSON output formats.\n\nModel Evaluation\n• Hold-out dataset with 500 manually labelled PRDs.\n  – Problem-Solution Matcher: F1 ≥0.83 overall.\n  – Product Type Classifier: micro-F1 ≥0.88, per-label precision ≥0.8.\n• Run k-fold cross-validation and attach a <PERSON><PERSON><PERSON> notebook with results to the MR.\n\nIntegration Tests\n• Spin up a local stack with ADVANCED_PRD=true.\n• POST 20 sample PRDs and assert:\n  – Response contains pattern_match block when detected.\n  – product_types array matches golden data.\n  – CoVe status=ok for standard PRD; status=warning when injected edge cases.\n\nRegression & Performance\n• k6 scenario issuing 100 RPS for 10 min; P95 latency increase ≤150 ms.\n\nManual QA\n• Toggle feature flag off and confirm legacy behaviour.\n• Review Prometheus dashboards for new metrics.\n\nCI/CD Gates\n• Block merge if any eval metric drops >2 % vs previous model or if latency budgets exceeded.", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Implement Problem-Solution Pattern Matcher", "description": "Encode common business context patterns to improve readiness detection and PRD outline suggestions", "details": "• Build trie-based matcher for patterns like \"We struggle with X\" ⇒ Problem, \"We propose Y\" ⇒ Solution.\n• Store matches in `conversation_patterns` table with offsets.\n• Provide `suggestedSections` array (e.g., \"Problem Statement\", \"Solution Overview\") for generation prompt.\n• Complexity: Medium\n• Dependencies: Context-Aware Classification Engine from MVP\n• Implementation Priority: 1st (good ROI, enhances PRD quality suggestions)", "status": "pending", "dependencies": [], "parentTaskId": 1}, {"id": 2, "title": "Implement Product Type Classifier Module", "description": "Classify conversation into B2B/B2C/mobile/web/SaaS etc. to tailor PRD sections", "details": "• Lightweight zero-shot Claude call with taxonomy prompt.\n• Cache result in `conversation_metadata` (`productType`, `confidence`).\n• Expose React hook `useProductType` for UI badges.\n• Complexity: Medium\n• Dependencies: Context-Aware Classification Engine from MVP\n• Implementation Priority: 2nd (nice feature, enables tailored PRD sections)", "status": "pending", "dependencies": [], "parentTaskId": 1}, {"id": 3, "title": "Implement 4-Phase Chain of Verification (CoVe)", "description": "Add graduated response system for edge cases: ask for clarification, re-check intent, escalate to human, or auto-resume", "details": "• Store `covePhase` in `conversation_modes`.\n• Phase 1: auto-clarify when confidence 0.4-0.6.\n• Phase 2: re-run classification with expanded history.\n• Phase 3: flag for human review via `support_tickets` table.\n• Phase 4: auto-resume after human override.\n• Implement `advanceCoVePhase` mutation invoked by detection pipeline.\n• Complexity: High\n• Dependencies: Multi-Category Indicator Collector & Context-Aware Classification Engine from MVP\n• Implementation Priority: 3rd (sophisticated but overkill for MVP validation)", "status": "pending", "dependencies": [], "parentTaskId": 1}]}], "metadata": {"created": "2025-07-26T04:04:11.400Z", "updated": "2025-07-26T04:04:42.265Z", "description": "Advanced features for post-MVP enhancement that can be implemented after core functionality is validated"}}}