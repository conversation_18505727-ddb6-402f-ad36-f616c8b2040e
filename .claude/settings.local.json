{"permissions": {"allow": ["mcp__taskmaster-ai__get_tasks", "mcp__taskmaster-ai__get_task", "WebFetch(domain:docs.convex.dev)", "WebFetch(domain:stack.convex.dev)", "mcp__taskmaster-ai__next_task", "Bash(pnpx convex dev:*)", "mcp__convex__status", "mcp__convex__tables", "WebFetch(domain:labs.convex.dev)", "Bash(git fetch:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(task-master show:*)", "mcp__playwright__browser_navigate", "mcp__convex__data", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__convex__envList", "Bash(pnpm lint:*)", "Bash(pnpm test:*)", "Bash(pnpm run test:*)", "Bash(pnpm run lint:*)", "Bash(pnpm run:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_type", "Bash(npx convex logs:*)", "Bash(npx convex components add:*)", "Bash(pnpm build:*)", "mcp__playwright__browser_close", "<PERSON><PERSON>(pnpm playwright test:*)", "mcp__github__create_issue", "mcp__taskmaster-ai__add_dependency", "mcp__taskmaster-ai__add_task", "mcp__taskmaster-ai__move_task", "mcp__taskmaster-ai__remove_dependency", "mcp__taskmaster-ai__set_task_status", "mcp__playwright__browser_press_key", "mcp__taskmaster-ai__add_subtask", "mcp__taskmaster-ai__update_subtask"]}, "enabledMcpjsonServers": ["taskmaster-ai", "convex", "playwright"]}