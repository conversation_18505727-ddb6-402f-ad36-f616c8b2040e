{"name": "prdgeneral", "version": "0.1.0", "private": true, "scripts": {"dev": "RUST_BACKTRACE=full next dev --turbopack", "dev:stable": "next dev", "build": "next build", "start": "next start", "lint": "next lint --dir src --dir convex", "lint:src": "next lint --dir src", "lint:convex": "next lint --dir convex", "lint:fix": "next lint --dir src --dir convex --fix", "test": "vitest run", "test:watch": "vitest", "test:debug": "node --inspect-brk \"$(npm exec --silent -- vitest)\" run --no-threads", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "clean:cache": "rm -rf .next && rm -rf node_modules/.cache"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@auth/core": "0.37.0", "@convex-dev/agent": "^0.1.15", "@convex-dev/auth": "^0.0.87", "@convex-dev/rate-limiter": "^0.2.12", "@tailwindcss/typography": "^0.5.16", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.4", "convex-test": "^0.0.38", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.10"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@edge-runtime/vm": "^5.0.0", "@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.4.4", "@playwright/mcp": "^0.0.31", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-plugin-react-hooks": "^5.2.0", "glob": "^11.0.3", "jsdom": "^26.1.0", "msw": "^2.10.4", "node-fetch": "^3.3.2", "tailwindcss": "^4", "task-master-ai": "^0.21.0", "typescript": "^5", "vite": "^7.0.6", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}