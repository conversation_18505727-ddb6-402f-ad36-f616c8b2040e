# All Ways to Create Convex Agents

## 1. Basic Agent Creation

### Minimal Agent
```ts
import { components } from "./_generated/api";
import { Agent } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";

// Simplest possible agent
const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
});
```

### Named Agent with Instructions
```ts
const supportAgent = new Agent(components.agent, {
  name: "Support Agent",
  chat: openai.chat("gpt-4o-mini"),
  instructions: "You are a helpful customer support assistant.",
});
```

## 2. Agent with Different LLM Providers

### OpenAI
```ts
import { openai } from "@ai-sdk/openai";

const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  textEmbedding: openai.embedding("text-embedding-3-small"),
});
```

### Anthropic Claude
```ts
import { anthropic } from "@ai-sdk/anthropic";

const agent = new Agent(components.agent, {
  chat: anthropic.chat("claude-3-sonnet-20240229"),
});
```

### Multiple Models
```ts
const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o"),              // Main chat model
  textEmbedding: openai.embedding("text-embedding-3-small"), // For RAG
});
```

## 3. Agent with Tools

### Using createTool (Recommended for Convex)
```ts
import { createTool } from "@convex-dev/agent";
import { z } from "zod";

const searchTool = createTool({
  description: "Search the database for information",
  args: z.object({
    query: z.string().describe("The search query"),
    limit: z.number().optional().describe("Max results"),
  }),
  handler: async (ctx, { query, limit = 10 }): Promise<string> => {
    // ctx has access to Convex context (runQuery, runMutation, etc.)
    const results = await ctx.runQuery(api.search.fullText, { query, limit });
    return JSON.stringify(results);
  },
});

const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  tools: { searchTool },
  maxSteps: 5, // Allow tool calls
});
```

### Using Standard AI SDK Tools
```ts
import { tool } from "ai";

const weatherTool = tool({
  description: "Get weather information",
  parameters: z.object({
    location: z.string().describe("City name"),
  }),
  execute: async ({ location }) => {
    // External API call
    const weather = await fetch(`/api/weather?city=${location}`);
    return weather.json();
  },
});

const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  tools: { weatherTool },
});
```

### Mixed Tools
```ts
const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  tools: {
    // Convex tool
    searchDatabase: createTool({ /* ... */ }),
    // AI SDK tool
    getWeather: tool({ /* ... */ }),
    // Another Convex tool
    createTicket: createTool({ /* ... */ }),
  },
  maxSteps: 10,
});
```

## 4. Agent with Advanced Configuration

### Full Configuration
```ts
const agent = new Agent(components.agent, {
  // Required
  chat: openai.chat("gpt-4o"),

  // Identity
  name: "Advanced Support Agent",
  instructions: "You are an expert customer support agent with access to our database.",

  // Tools
  tools: { searchTool, createTicketTool },
  maxSteps: 5,
  maxRetries: 3,

  // RAG/Embeddings
  textEmbedding: openai.embedding("text-embedding-3-small"),

  // Context control
  contextOptions: {
    excludeToolMessages: false,
    recentMessages: 50,
    searchOptions: {
      limit: 20,
      textSearch: true,
      vectorSearch: true,
      messageRange: { before: 3, after: 1 },
    },
    searchOtherThreads: true,
  },

  // Message storage
  storageOptions: {
    saveMessages: "all", // "none" | "promptAndOutput" | "all"
  },

  // Usage tracking
  usageHandler: async (ctx, { userId, model, usage }) => {
    await ctx.runMutation(api.billing.trackUsage, {
      userId,
      model,
      tokens: usage.totalTokens,
      cost: calculateCost(model, usage),
    });
  },

  // Debug logging
  rawRequestResponseHandler: async (ctx, { request, response }) => {
    console.log("LLM Request:", request);
    console.log("LLM Response:", response);
  },
});
```

## 5. Dynamic Agent Creation

### Agent Factory Function
```ts
function createCustomerAgent(teamId: Id<"teams">) {
  return new Agent(components.agent, {
    name: `Team ${teamId} Agent`,
    chat: openai.chat("gpt-4o-mini"),
    instructions: `You help customers for team ${teamId}.`,
    tools: {
      searchTeamData: createTool({
        description: "Search team-specific data",
        args: z.object({ query: z.string() }),
        handler: async (ctx, { query }) => {
          return await ctx.runQuery(api.teams.search, { teamId, query });
        },
      }),
    },
  });
}

// Usage
export const handleCustomerQuery = action({
  args: { teamId: v.id("teams"), prompt: v.string() },
  handler: async (ctx, { teamId, prompt }) => {
    const agent = createCustomerAgent(teamId);
    const { thread } = await agent.createThread(ctx);
    return await thread.generateText({ prompt });
  },
});
```

### Runtime Tool Creation
```ts
export const createAgentWithDynamicTools = action({
  args: { userId: v.string(), availableTools: v.array(v.string()) },
  handler: async (ctx, { userId, availableTools }) => {
    const tools: Record<string, any> = {};

    // Add tools based on user permissions
    if (availableTools.includes("search")) {
      tools.search = createTool({ /* search tool */ });
    }
    if (availableTools.includes("create_ticket")) {
      tools.createTicket = createTool({ /* ticket tool */ });
    }

    const agent = new Agent(components.agent, {
      chat: openai.chat("gpt-4o-mini"),
      tools,
    });

    return agent;
  },
});
```

## 6. Specialized Agent Patterns

### RAG Agent
```ts
const ragAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o"),
  textEmbedding: openai.embedding("text-embedding-3-small"),
  instructions: "You answer questions using the provided knowledge base.",
  tools: {
    searchKnowledge: createTool({
      description: "Search the knowledge base",
      args: z.object({ query: z.string() }),
      handler: async (ctx, { query }) => {
        const results = await rag.search(ctx, { query, limit: 5 });
        return results.text;
      },
    }),
  },
  contextOptions: {
    searchOptions: {
      vectorSearch: true,
      textSearch: true,
      limit: 10,
    },
  },
});
```

### Workflow Agent
```ts
const workflowAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o"),
  tools: {
    approveRequest: createTool({ /* ... */ }),
    requestMoreInfo: createTool({ /* ... */ }),
    escalateToHuman: createTool({ /* ... */ }),
  },
  maxSteps: 10, // Allow multi-step workflows
});

// Expose as workflow-compatible actions
export const processRequest = workflowAgent.asTextAction();
export const createThread = workflowAgent.createThreadMutation();
export const saveMessages = workflowAgent.asSaveMessagesMutation();
```

### Streaming Agent
```ts
const streamingAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  instructions: "Provide detailed responses with streaming.",
});

export const streamResponse = action({
  args: { threadId: v.string(), prompt: v.string() },
  handler: async (ctx, { threadId, prompt }) => {
    const { thread } = await streamingAgent.continueThread(ctx, { threadId });
    return await thread.streamText({
      prompt,
      saveStreamDeltas: true, // Enable live streaming updates
    });
  },
});
```

## 7. Agent Composition Patterns

### Multiple Specialized Agents
```ts
// Different agents for different purposes
const summaryAgent = new Agent(components.agent, {
  name: "Summary Agent",
  chat: openai.chat("gpt-4o-mini"),
  instructions: "You create concise summaries.",
});

const analysisAgent = new Agent(components.agent, {
  name: "Analysis Agent",
  chat: openai.chat("gpt-4o"),
  instructions: "You provide detailed analysis.",
  tools: { searchData: createTool({ /* ... */ }) },
});

const creativityAgent = new Agent(components.agent, {
  name: "Creative Agent",
  chat: anthropic.chat("claude-3-sonnet-20240229"),
  instructions: "You help with creative writing and ideas.",
});
```

### Agent Router
```ts
export const routeToAgent = action({
  args: { prompt: v.string(), type: v.union(v.literal("summary"), v.literal("analysis"), v.literal("creative")) },
  handler: async (ctx, { prompt, type }) => {
    let agent: Agent;

    switch (type) {
      case "summary":
        agent = summaryAgent;
        break;
      case "analysis":
        agent = analysisAgent;
        break;
      case "creative":
        agent = creativityAgent;
        break;
    }

    const { thread } = await agent.createThread(ctx);
    return await thread.generateText({ prompt });
  },
});
```

## 8. Advanced Workflow Integration

### Exposing Agent as Convex Functions
```ts
// For use in workflows
export const createThread = supportAgent.createThreadMutation();
export const generateText = supportAgent.asTextAction({ maxSteps: 5 });
export const generateObject = supportAgent.asObjectAction({
  schema: z.object({
    intent: z.string(),
    confidence: z.number(),
    response: z.string(),
  }),
});
export const saveMessages = supportAgent.asSaveMessagesMutation();

// Use in workflow
const workflow = new WorkflowManager(components.workflow);

export const supportWorkflow = workflow.define({
  args: { prompt: v.string() },
  handler: async (step, { prompt }) => {
    const { threadId } = await step.runMutation(internal.agents.createThread, {});
    const response = await step.runAction(internal.agents.generateText, {
      threadId,
      prompt,
    });
    return response;
  },
});
```

## Key Considerations

**When to use each pattern:**

1. **Basic Agent**: Simple Q&A, basic chatbots
2. **Agent with Tools**: When you need database access, API calls, or actions
3. **RAG Agent**: Knowledge base applications, document Q&A
4. **Workflow Agent**: Multi-step processes, approval workflows
5. **Dynamic Agents**: Multi-tenant applications, permission-based tools
6. **Specialized Agents**: Different LLMs for different tasks
7. **Streaming Agents**: Real-time chat interfaces

**Performance tips:**
- Use `textEmbedding` only when needed for RAG
- Set appropriate `maxSteps` for tool usage
- Consider `contextOptions` for large conversation histories
- Use workflow patterns for complex, long-running processes
