I'll search for documentation on Convex agents in the knowledge base to provide you with comprehensive information.# Convex Agents Documentation

## Overview

Convex provides powerful building blocks for building agentic AI applications, leveraging Components and existing Convex features. With Convex, you can separate your long-running agentic workflows from your UI, without the user losing reactivity and interactivity. The message history with an LLM is persisted by default, live updating on every client, and easily composed with other Convex features using code rather than configuration.

## Getting Started

### Installation

1. **Install the component package:**
```bash
npm install @convex-dev/agent
```

2. **Configure the component:**
Create a `convex.config.ts` file in your app's `convex/` folder:

```ts
// convex/convex.config.ts
import { defineApp } from "convex/server";
import agent from "@convex-dev/agent/convex.config";

const app = defineApp();
app.use(agent);

export default app;
```

3. **Generate component code:**
```bash
npx convex dev
```

### Basic Agent Definition

```ts
import { components } from "./_generated/api";
import { Agent } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";

const agent = new Agent(components.agent, {
  name: "My Agent",
  chat: openai.chat("gpt-4o-mini"),
});

export const helloWorld = action({
  args: { prompt: v.string() },
  handler: async (ctx, { prompt }) => {
    const userId = await getAuthUserId(ctx);
    const { thread } = await agent.createThread(ctx, { userId });
    const result = await thread.generateText({ prompt });
    return result.text;
  },
});
```

## Core Concepts

### 1. Agents

Agents organize LLM prompting with associated models, prompts, and tools. They can generate and stream both text and objects.

**Agent Configuration:**
```ts
import { tool } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { Agent, createTool } from "@convex-dev/agent";

const supportAgent = new Agent(components.agent, {
  // Required: The chat completions model
  chat: openai.chat("gpt-4o-mini"),

  // Optional: Agent name for attribution
  name: "Support Agent",

  // Optional: Default system prompt
  instructions: "You are a helpful assistant.",

  // Optional: Tools available to the agent
  tools: {
    // Convex tool with access to context
    myConvexTool: createTool({
      description: "My Convex tool",
      args: z.object({
        query: z.string().describe("The search query")
      }),
      handler: async (ctx, args): Promise<string> => {
        return "Hello, world!";
      },
    }),
    // Standard AI SDK tool
    myTool: tool({ description, parameters, execute: () => {}}),
  },

  // Optional: Embedding model for vector search
  textEmbedding: openai.embedding("text-embedding-3-small"),

  // Optional: Context configuration
  contextOptions: {
    excludeToolMessages: true,
    recentMessages: 100,
    searchOptions: {
      limit: 10,
      textSearch: false,
      vectorSearch: false,
    },
  },

  // Optional: Message storage configuration
  storageOptions: {
    saveMessages: "promptAndOutput", // "all" | "none" | "promptAndOutput"
  },

  // Optional: Tool calling configuration
  maxSteps: 1, // Set > 1 for automatic tool calls
  maxRetries: 3,

  // Optional: Usage tracking
  usageHandler: async (ctx, { model, usage }) => {
    // Log, save usage to database, etc.
  },
});
```

### 2. Threads

Threads group messages together in a linear conversation history. All messages are associated with a thread, and threads can be associated with users.

**Creating a Thread:**
```ts
const { threadId, thread } = await agent.createThread(ctx, {
  userId,
  title: "Support Request",
  summary: "User needs help with billing",
});
```

**Continuing a Thread:**
```ts
export const continueThread = action({
  args: { prompt: v.string(), threadId: v.string() },
  handler: async (ctx, { prompt, threadId }) => {
    const { thread } = await agent.continueThread(ctx, { threadId });
    const result = await thread.generateText({ prompt });
    return result.text;
  },
});
```

**Thread Management:**
```ts
// Delete a thread
await agent.deleteThreadAsync(ctx, { threadId });

// Delete all threads for a user
await agent.deleteThreadsByUserId(ctx, { userId });

// Get all threads for a user
const threads = await ctx.runQuery(
  components.agent.threads.listThreadsByUserId,
  { userId, paginationOpts: { cursor: null, numItems: 10 } }
);
```

### 3. Messages

Messages are the individual communications within threads, including user messages, agent responses, tool calls, and tool results.

**Basic Message Generation:**
```ts
export const generateReply = action({
  args: { prompt: v.string(), threadId: v.string() },
  handler: async (ctx, { prompt, threadId }) => {
    const result = await agent.generateText(ctx, { threadId }, { prompt });
    return result.text;
  },
});
```

**Asynchronous Message Generation:**
```ts
// Step 1: Save user message in mutation
export const sendMessage = mutation({
  args: { threadId: v.id("threads"), prompt: v.string() },
  handler: async (ctx, { threadId, prompt }) => {
    const userId = await getUserId(ctx);
    const { messageId } = await saveMessage(ctx, components.agent, {
      threadId,
      userId,
      prompt,
      skipEmbeddings: true,
    });
    await ctx.scheduler.runAfter(0, internal.example.generateResponseAsync, {
      threadId,
      promptMessageId: messageId,
    });
  },
});

// Step 2: Generate response in action
export const generateResponseAsync = internalAction({
  args: { threadId: v.string(), promptMessageId: v.string() },
  handler: async (ctx, { threadId, promptMessageId }) => {
    await agent.generateText(ctx, { threadId }, { promptMessageId });
  },
});
```

**Streaming Messages:**
```ts
export const streamResponse = action({
  args: { prompt: v.string(), threadId: v.string() },
  handler: async (ctx, { prompt, threadId }) => {
    const result = await agent.streamText(
      ctx,
      { threadId },
      {
        prompt,
        saveStreamDeltas: true // Saves chunks for live updates
      }
    );
    return result.textStream;
  },
});
```

**Listing Messages:**
```ts
import { listMessages } from "@convex-dev/agent";

const messages = await listMessages(ctx, components.agent, {
  threadId,
  excludeToolMessages: true,
  paginationOpts: { cursor: null, numItems: 10 },
});
```

### 4. Tools

Tools allow agents to call external functions or services during conversations.

**Creating Convex Tools:**
```ts
export const searchTool = createTool({
  description: "Search for information in the database",
  args: z.object({
    query: z.string().describe("The search query")
  }),
  handler: async (ctx, { query }): Promise<Array<SearchResult>> => {
    // ctx has agent, userId, threadId, messageId
    // as well as ActionCtx properties
    const results = await ctx.runQuery(api.search.searchDocs, { query });
    return results;
  },
});

// Use in agent
const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  tools: { searchTool },
  maxSteps: 5, // Allow multiple tool calls
});
```

**Tool Call Handling:**
Tools are automatically called when `maxSteps > 1`. Tool calls and results are stored as messages in the thread.

### 5. Context and RAG

The agent automatically includes conversation context and supports Retrieval-Augmented Generation (RAG).

**Context Configuration:**
```ts
const result = await agent.generateText(
  ctx,
  { threadId },
  { prompt },
  {
    contextOptions: {
      excludeToolMessages: true,
      recentMessages: 100,
      searchOptions: {
        limit: 10,
        textSearch: true,
        vectorSearch: true,
        messageRange: { before: 2, after: 1 },
      },
      searchOtherThreads: false, // Set true to search across user's threads
    },
  }
);
```

**RAG Approaches:**

1. **Prompt-based RAG** (automatic context injection):
```ts
const context = await rag.search(ctx, {
  namespace: "global",
  query: userPrompt,
  limit: 10,
});

const result = await thread.generateText({
  prompt: `# Context:\n\n${context.text}\n\n---\n\n# Question:\n\n${userPrompt}`,
});
```

2. **Tool-based RAG** (LLM decides when to search):
```ts
const searchContext = createTool({
  description: "Search for context related to this user prompt",
  args: z.object({
    query: z.string().describe("Describe the context you're looking for")
  }),
  handler: async (ctx, { query }) => {
    const context = await rag.search(ctx, { namespace: userId, query });
    return context.text;
  },
});
```

### 6. Workflows

Workflows enable multi-step operations that can span agents and users, with durability and reliability.

**Exposing Agent as Actions:**
```ts
// Create thread mutation
export const createThread = supportAgent.createThreadMutation();

// Text generation action
export const getSupport = supportAgent.asTextAction({
  maxSteps: 10,
});

// Object generation action
export const getStructuredSupport = supportAgent.asObjectAction({
  schema: z.object({
    analysis: z.string().describe("Analysis of the request"),
    suggestion: z.string().describe("Suggested action"),
  }),
});

// Save messages mutation
export const saveMessages = supportAgent.asSaveMessagesMutation();
```

**Using in Workflows:**
```ts
const workflow = new WorkflowManager(components.workflow);

export const supportWorkflow = workflow.define({
  args: { prompt: v.string(), userId: v.string() },
  handler: async (step, { prompt, userId }) => {
    const { threadId } = await step.runMutation(internal.example.createThread, {
      userId,
      title: "Support Request",
    });

    const suggestion = await step.runAction(internal.example.getSupport, {
      threadId,
      userId,
      prompt,
    });

    const { object } = await step.runAction(
      internal.example.getStructuredSupport,
      { userId, message: suggestion }
    );

    return object.suggestion;
  },
});
```

## Advanced Features

### Rate Limiting

Control the rate at which users can interact with agents:

```ts
import { MINUTE, RateLimiter, SECOND } from "@convex-dev/rate-limiter";

export const rateLimiter = new RateLimiter(components.rateLimiter, {
  sendMessage: {
    kind: "fixed window",
    period: 5 * SECOND,
    rate: 1,
    capacity: 2,
  },
  tokenUsagePerUser: {
    kind: "token bucket",
    period: MINUTE,
    rate: 2000,
    capacity: 10000,
  },
});

// Use in actions
export const sendMessage = mutation({
  args: { prompt: v.string(), threadId: v.string() },
  handler: async (ctx, { prompt, threadId }) => {
    const userId = await getUserId(ctx);

    // Check rate limits
    await rateLimiter.limit(ctx, "sendMessage", { key: userId, throws: true });

    // Estimate and check token usage
    const estimatedTokens = await estimateTokens(ctx, threadId, prompt);
    await rateLimiter.check(ctx, "tokenUsagePerUser", {
      key: userId,
      count: estimatedTokens,
      throws: true,
    });

    // Proceed with message generation...
  },
});
```

### Usage Tracking

Track token usage for billing and monitoring:

```ts
const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  usageHandler: async (ctx, args) => {
    const {
      userId, threadId, agentName,
      model, provider,
      usage, providerMetadata
    } = args;

    // Store usage in database
    await ctx.runMutation(internal.usage.insertUsage, {
      userId,
      model,
      usage: usage.totalTokens,
      billingPeriod: getBillingPeriod(Date.now()),
    });
  },
});
```

### Files and Images

Agents support files and images in messages with automatic file storage:

```ts
// Files are automatically handled in message content
const result = await thread.generateText({
  messages: [
    {
      role: "user",
      content: [
        { type: "text", text: "Analyze this image:" },
        { type: "image", image: imageData }
      ]
    }
  ]
});
```

### Human Agents

Allow humans to participate as agents in conversations:

```ts
// Save a message from a human acting as an agent
import { saveMessage } from "@convex-dev/agent";

export const humanReply = mutation({
  args: { threadId: v.string(), message: v.string() },
  handler: async (ctx, { threadId, message }) => {
    const userId = await getUserId(ctx);
    await saveMessage(ctx, components.agent, {
      threadId,
      userId,
      agentName: "Human Support",
      message: { role: "assistant", content: message },
    });
  },
});
```

### Debugging

**Agent Playground:**
Access the built-in playground to test, debug, and develop agents with full metadata inspection.

**Raw Request/Response Logging:**
```ts
const agent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o-mini"),
  rawRequestResponseHandler: async (ctx, { request, response }) => {
    console.log("LLM Request:", request);
    console.log("LLM Response:", response);
  },
});
```

**Database Inspection:**
View agent data in the Convex dashboard under the agent component tables:
- `threads` - Thread metadata
- `messages` - Individual messages
- `streamingMessages` - Streaming message state
- `files` - File attachments

## Agent Mode for Cloud Development

For cloud-based coding agents like Cursor background agents, use anonymous development mode:

```bash
CONVEX_AGENT_MODE=anonymous npx convex dev
```

This allows agents to use separate Convex backends without conflicting with your development environment.

## Best Practices

1. **Use mutations for saving messages** and actions for generation to enable optimistic UI updates
2. **Implement proper authorization** for thread access
3. **Set appropriate rate limits** to prevent abuse
4. **Track usage** for billing and monitoring
5. **Use tools judiciously** - set `maxSteps` appropriately
6. **Implement error handling** for tool calls and LLM requests
7. **Use vector search** for large conversation histories
8. **Consider workflows** for complex multi-step operations

This documentation provides a comprehensive guide to using Convex agents. For working examples and advanced patterns, refer to the [Convex Agent GitHub repository](https://github.com/get-convex/agent).