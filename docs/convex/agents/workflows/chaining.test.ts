import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { WorkflowId, WorkflowManager } from "@convex-dev/workflow";
import { createThread, saveMessage } from "@convex-dev/agent";
import { components, internal } from "../_generated/api";
import { weatherAgent } from "../agents/weather";
import { fashionAgent } from "../agents/fashion";
import { getAuthUserId } from "../utils";
import {
  getAdvice,
  weatherAgentWorkflow,
  startWorkflow
} from "./chaining";

// Mock all external dependencies
vi.mock("@convex-dev/workflow");
vi.mock("@convex-dev/agent");
vi.mock("../_generated/api");
vi.mock("../_generated/server");
vi.mock("../agents/weather");
vi.mock("../agents/fashion");
vi.mock("../utils");
vi.mock("zod");

describe("Chaining Workflow Tests", () => {
  let mockCtx: any;
  let mockStep: any;
  let mockWorkflow: any;
  
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup mock context
    mockCtx = {
      db: vi.fn(),
      storage: vi.fn(),
      scheduler: vi.fn(),
    };
    
    // Setup mock step for workflow testing
    mockStep = {
      runAction: vi.fn(),
      runMutation: vi.fn(),
      runQuery: vi.fn(),
      sleep: vi.fn(),
    };
    
    // Setup mock workflow manager
    mockWorkflow = {
      define: vi.fn(),
      start: vi.fn(),
    };
    
    (WorkflowManager as vi.Mock).mockImplementation(() => mockWorkflow);
  });
  
  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("getAdvice action", () => {
    it("should successfully chain weather and fashion agent calls", async () => {
      // Arrange
      const mockUserId = "user123";
      const mockLocation = "San Francisco";
      const mockThreadId = "thread456";
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (weatherAgent.generateText as vi.Mock).mockResolvedValue("Sunny, 75°F");
      (fashionAgent.generateText as vi.Mock).mockResolvedValue("Wear light clothing");
      
      const args = { location: mockLocation, threadId: mockThreadId };
      
      // Act
      await getAdvice.handler(mockCtx, args);
      
      // Assert
      expect(getAuthUserId).toHaveBeenCalledWith(mockCtx);
      expect(weatherAgent.generateText).toHaveBeenCalledWith(
        mockCtx,
        { threadId: mockThreadId, userId: mockUserId },
        { prompt: `What is the weather in ${mockLocation}?` }
      );
      expect(fashionAgent.generateText).toHaveBeenCalledWith(
        mockCtx,
        { threadId: mockThreadId, userId: mockUserId },
        { prompt: `What should I wear based on the weather?` }
      );
    });

    it("should handle authentication failure gracefully", async () => {
      // Arrange
      const mockError = new Error("Authentication failed");
      (getAuthUserId as vi.Mock).mockRejectedValue(mockError);
      
      const args = { location: "New York", threadId: "thread789" };
      
      // Act & Assert
      await expect(getAdvice.handler(mockCtx, args)).rejects.toThrow("Authentication failed");
      expect(weatherAgent.generateText).not.toHaveBeenCalled();
      expect(fashionAgent.generateText).not.toHaveBeenCalled();
    });

    it("should handle weather agent failure", async () => {
      // Arrange
      const mockUserId = "user123";
      const mockError = new Error("Weather service unavailable");
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (weatherAgent.generateText as vi.Mock).mockRejectedValue(mockError);
      
      const args = { location: "Chicago", threadId: "thread101" };
      
      // Act & Assert
      await expect(getAdvice.handler(mockCtx, args)).rejects.toThrow("Weather service unavailable");
      expect(fashionAgent.generateText).not.toHaveBeenCalled();
    });

    it("should handle fashion agent failure after successful weather call", async () => {
      // Arrange
      const mockUserId = "user123";
      const mockError = new Error("Fashion service unavailable");
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (weatherAgent.generateText as vi.Mock).mockResolvedValue("Rainy, 60°F");
      (fashionAgent.generateText as vi.Mock).mockRejectedValue(mockError);
      
      const args = { location: "Seattle", threadId: "thread202" };
      
      // Act & Assert
      await expect(getAdvice.handler(mockCtx, args)).rejects.toThrow("Fashion service unavailable");
      expect(weatherAgent.generateText).toHaveBeenCalled();
    });

    it("should handle empty location string", async () => {
      // Arrange
      const mockUserId = "user123";
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (weatherAgent.generateText as vi.Mock).mockResolvedValue("Unknown location");
      (fashionAgent.generateText as vi.Mock).mockResolvedValue("General advice");
      
      const args = { location: "", threadId: "thread303" };
      
      // Act
      await getAdvice.handler(mockCtx, args);
      
      // Assert
      expect(weatherAgent.generateText).toHaveBeenCalledWith(
        mockCtx,
        { threadId: "thread303", userId: mockUserId },
        { prompt: "What is the weather in ?" }
      );
    });

    it("should handle special characters in location", async () => {
      // Arrange
      const mockUserId = "user123";
      const specialLocation = "São Paulo, Brazil 🇧🇷";
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (weatherAgent.generateText as vi.Mock).mockResolvedValue("Tropical weather");
      (fashionAgent.generateText as vi.Mock).mockResolvedValue("Light summer clothing");
      
      const args = { location: specialLocation, threadId: "thread404" };
      
      // Act
      await getAdvice.handler(mockCtx, args);
      
      // Assert
      expect(weatherAgent.generateText).toHaveBeenCalledWith(
        mockCtx,
        { threadId: "thread404", userId: mockUserId },
        { prompt: `What is the weather in ${specialLocation}?` }
      );
    });
  });

  describe("weatherAgentWorkflow", () => {
    it("should execute workflow steps in correct order", async () => {
      // Arrange
      const mockLocation = "Denver";
      const mockThreadId = "thread505";
      const mockWeatherMessage = { messageId: "weather123" };
      const mockFashionMessage = { messageId: "fashion456" };
      const mockForecast = "Snow expected, 25°F";
      const mockFashionAdvice = { 
        object: { hat: "Beanie", tops: "Coat", bottoms: "Jeans", shoes: "Boots" }
      };
      
      (saveMessage as vi.Mock)
        .mockResolvedValueOnce(mockWeatherMessage)
        .mockResolvedValueOnce(mockFashionMessage);
      
      mockStep.runAction
        .mockResolvedValueOnce(mockForecast)
        .mockResolvedValueOnce(mockFashionAdvice);
      
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation();
      
      // Act
      await weatherAgentWorkflow.handler(mockStep, { location: mockLocation, threadId: mockThreadId });
      
      // Assert
      expect(saveMessage).toHaveBeenNthCalledWith(1, mockStep, components.agent, {
        threadId: mockThreadId,
        prompt: `What is the weather in ${mockLocation}?`
      });
      
      expect(mockStep.runAction).toHaveBeenNthCalledWith(1,
        internal.workflows.chaining.getForecast,
        { promptMessageId: mockWeatherMessage.messageId, threadId: mockThreadId },
        { retry: true }
      );
      
      expect(saveMessage).toHaveBeenNthCalledWith(2, mockStep, components.agent, {
        threadId: mockThreadId,
        prompt: "What should I wear based on the weather?"
      });
      
      expect(mockStep.runAction).toHaveBeenNthCalledWith(2,
        internal.workflows.chaining.getFashionAdvice,
        { promptMessageId: mockFashionMessage.messageId, threadId: mockThreadId },
        {
          retry: { maxAttempts: 5, initialBackoffMs: 1000, base: 2 }
        }
      );
      
      expect(consoleSpy).toHaveBeenCalledWith("Weather forecast:", mockForecast);
      expect(consoleSpy).toHaveBeenCalledWith("Fashion advice:", mockFashionAdvice.object);
      
      consoleSpy.mockRestore();
    });

    it("should handle saveMessage failure for weather query", async () => {
      // Arrange
      const mockError = new Error("Failed to save weather message");
      (saveMessage as vi.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(
        weatherAgentWorkflow.handler(mockStep, { location: "Boston", threadId: "thread606" })
      ).rejects.toThrow("Failed to save weather message");
      
      expect(mockStep.runAction).not.toHaveBeenCalled();
    });

    it("should handle weather forecast action failure with retry", async () => {
      // Arrange
      const mockWeatherMessage = { messageId: "weather789" };
      const mockError = new Error("Weather service timeout");
      
      (saveMessage as vi.Mock).mockResolvedValue(mockWeatherMessage);
      mockStep.runAction.mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(
        weatherAgentWorkflow.handler(mockStep, { location: "Phoenix", threadId: "thread707" })
      ).rejects.toThrow("Weather service timeout");
      
      expect(mockStep.runAction).toHaveBeenCalledWith(
        internal.workflows.chaining.getForecast,
        { promptMessageId: mockWeatherMessage.messageId, threadId: "thread707" },
        { retry: true }
      );
    });

    it("should handle fashion advice action failure with custom retry config", async () => {
      // Arrange
      const mockWeatherMessage = { messageId: "weather101" };
      const mockFashionMessage = { messageId: "fashion202" };
      const mockForecast = "Cloudy, 65°F";
      const mockError = new Error("Fashion API rate limit exceeded");
      
      (saveMessage as vi.Mock)
        .mockResolvedValueOnce(mockWeatherMessage)
        .mockResolvedValueOnce(mockFashionMessage);
      
      mockStep.runAction
        .mockResolvedValueOnce(mockForecast)
        .mockRejectedValueOnce(mockError);
      
      // Act & Assert
      await expect(
        weatherAgentWorkflow.handler(mockStep, { location: "Miami", threadId: "thread808" })
      ).rejects.toThrow("Fashion API rate limit exceeded");
      
      expect(mockStep.runAction).toHaveBeenNthCalledWith(2,
        internal.workflows.chaining.getFashionAdvice,
        { promptMessageId: mockFashionMessage.messageId, threadId: "thread808" },
        {
          retry: { maxAttempts: 5, initialBackoffMs: 1000, base: 2 }
        }
      );
    });

    it("should handle workflow with empty location", async () => {
      // Arrange
      const mockWeatherMessage = { messageId: "weather303" };
      const mockFashionMessage = { messageId: "fashion404" };
      
      (saveMessage as vi.Mock)
        .mockResolvedValueOnce(mockWeatherMessage)
        .mockResolvedValueOnce(mockFashionMessage);
      
      mockStep.runAction
        .mockResolvedValueOnce("Location not specified")
        .mockResolvedValueOnce({ object: { hat: "None", tops: "Any", bottoms: "Any", shoes: "Any" }});
      
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation();
      
      // Act
      await weatherAgentWorkflow.handler(mockStep, { location: "", threadId: "thread909" });
      
      // Assert
      expect(saveMessage).toHaveBeenNthCalledWith(1, mockStep, components.agent, {
        threadId: "thread909",
        prompt: "What is the weather in ?"
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe("startWorkflow mutation", () => {
    it("should successfully create thread and start workflow", async () => {
      // Arrange
      const mockUserId = "user456";
      const mockThreadId = "thread111";
      const mockWorkflowId = "workflow222" as WorkflowId;
      const mockLocation = "Portland";
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock).mockResolvedValue(mockThreadId);
      mockWorkflow.start.mockResolvedValue(mockWorkflowId);
      
      // Act
      const result = await startWorkflow.handler(mockCtx, { location: mockLocation });
      
      // Assert
      expect(getAuthUserId).toHaveBeenCalledWith(mockCtx);
      expect(createThread).toHaveBeenCalledWith(mockCtx, components.agent, {
        userId: mockUserId,
        title: `Weather in ${mockLocation}`
      });
      expect(mockWorkflow.start).toHaveBeenCalledWith(
        mockCtx,
        internal.workflows.chaining.weatherAgentWorkflow,
        { location: mockLocation, threadId: mockThreadId }
      );
      expect(result).toEqual({ threadId: mockThreadId, workflowId: mockWorkflowId });
    });

    it("should handle authentication failure", async () => {
      // Arrange
      const mockError = new Error("User not authenticated");
      (getAuthUserId as vi.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(
        startWorkflow.handler(mockCtx, { location: "Atlanta" })
      ).rejects.toThrow("User not authenticated");
      
      expect(createThread).not.toHaveBeenCalled();
      expect(mockWorkflow.start).not.toHaveBeenCalled();
    });

    it("should handle thread creation failure", async () => {
      // Arrange
      const mockUserId = "user789";
      const mockError = new Error("Failed to create thread");
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock).mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(
        startWorkflow.handler(mockCtx, { location: "Las Vegas" })
      ).rejects.toThrow("Failed to create thread");
      
      expect(mockWorkflow.start).not.toHaveBeenCalled();
    });

    it("should handle workflow start failure", async () => {
      // Arrange
      const mockUserId = "user101";
      const mockThreadId = "thread333";
      const mockError = new Error("Workflow manager unavailable");
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock).mockResolvedValue(mockThreadId);
      mockWorkflow.start.mockRejectedValue(mockError);
      
      // Act & Assert
      await expect(
        startWorkflow.handler(mockCtx, { location: "Austin" })
      ).rejects.toThrow("Workflow manager unavailable");
      
      expect(createThread).toHaveBeenCalled();
    });

    it("should create proper thread title with special characters", async () => {
      // Arrange
      const mockUserId = "user202";
      const mockThreadId = "thread444";
      const mockWorkflowId = "workflow555" as WorkflowId;
      const specialLocation = "Zürich, Switzerland";
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock).mockResolvedValue(mockThreadId);
      mockWorkflow.start.mockResolvedValue(mockWorkflowId);
      
      // Act
      await startWorkflow.handler(mockCtx, { location: specialLocation });
      
      // Assert
      expect(createThread).toHaveBeenCalledWith(mockCtx, components.agent, {
        userId: mockUserId,
        title: `Weather in ${specialLocation}`
      });
    });

    it("should handle empty location in workflow start", async () => {
      // Arrange
      const mockUserId = "user303";
      const mockThreadId = "thread555";
      const mockWorkflowId = "workflow666" as WorkflowId;
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock).mockResolvedValue(mockThreadId);
      mockWorkflow.start.mockResolvedValue(mockWorkflowId);
      
      // Act
      const result = await startWorkflow.handler(mockCtx, { location: "" });
      
      // Assert
      expect(createThread).toHaveBeenCalledWith(mockCtx, components.agent, {
        userId: mockUserId,
        title: "Weather in "
      });
      expect(result).toEqual({ threadId: mockThreadId, workflowId: mockWorkflowId });
    });
  });

  describe("Agent Action Exports", () => {
    it("should export getForecast with correct configuration", () => {
      // Arrange
      const mockAsTextAction = vi.fn().mockReturnValue("mockForecastAction");
      (weatherAgent.asTextAction as vi.Mock) = mockAsTextAction;
      
      // Act

      // Assert
      expect(weatherAgent.asTextAction).toHaveBeenCalledWith({
        maxSteps: 3
      });
    });

    it("should export getFashionAdvice with correct schema", () => {
      // Arrange  
      const mockAsObjectAction = vi.fn().mockReturnValue("mockFashionAction");
      (fashionAgent.asObjectAction as vi.Mock) = mockAsObjectAction;
      
      // Act

      // Assert
      expect(fashionAgent.asObjectAction).toHaveBeenCalledWith({
        schema: expect.objectContaining({
          _def: expect.any(Object)
        })
      });
    });
  });

  describe("Integration Tests", () => {
    it("should handle complete workflow execution end-to-end", async () => {
      // Arrange - Full workflow simulation
      const mockUserId = "user404";
      const mockThreadId = "thread666";
      const mockWorkflowId = "workflow777" as WorkflowId;
      const mockLocation = "San Diego";
      
      const mockWeatherMessage = { messageId: "weather404" };
      const mockFashionMessage = { messageId: "fashion505" };
      const mockForecast = "Sunny and warm, 78°F";
      const mockFashionAdvice = { 
        object: { 
          hat: "Sun hat", 
          tops: "T-shirt", 
          bottoms: "Shorts", 
          shoes: "Sandals" 
        }
      };
      
      // Setup all mocks
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock).mockResolvedValue(mockThreadId);
      mockWorkflow.start.mockResolvedValue(mockWorkflowId);
      
      (saveMessage as vi.Mock)
        .mockResolvedValueOnce(mockWeatherMessage)
        .mockResolvedValueOnce(mockFashionMessage);
      
      mockStep.runAction
        .mockResolvedValueOnce(mockForecast)
        .mockResolvedValueOnce(mockFashionAdvice);
      
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation();
      
      // Act - Start workflow
      const startResult = await startWorkflow.handler(mockCtx, { location: mockLocation });
      
      // Act - Execute workflow 
      await weatherAgentWorkflow.handler(mockStep, { location: mockLocation, threadId: mockThreadId });
      
      // Assert - Verify complete flow
      expect(startResult).toEqual({ threadId: mockThreadId, workflowId: mockWorkflowId });
      expect(consoleSpy).toHaveBeenCalledWith("Weather forecast:", mockForecast);
      expect(consoleSpy).toHaveBeenCalledWith("Fashion advice:", mockFashionAdvice.object);
      
      consoleSpy.mockRestore();
    });

    it("should handle workflow retry scenarios", async () => {
      // Arrange - Simulate retry scenario
      const mockWeatherMessage = { messageId: "weather505" };
      const mockFashionMessage = { messageId: "fashion606" };
      
      (saveMessage as vi.Mock)
        .mockResolvedValueOnce(mockWeatherMessage)
        .mockResolvedValueOnce(mockFashionMessage);
      
      // First call fails, second succeeds
      mockStep.runAction
        .mockResolvedValueOnce("Partly cloudy, 70°F")
        .mockRejectedValueOnce(new Error("Temporary failure"))
        .mockResolvedValueOnce({ 
          object: { 
            hat: "Cap", 
            tops: "Light jacket", 
            bottoms: "Pants", 
            shoes: "Sneakers" 
          }
        });
      
      // Act & Assert - First attempt should fail at fashion step
      await expect(
        weatherAgentWorkflow.handler(mockStep, { location: "Nashville", threadId: "thread777" })
      ).rejects.toThrow("Temporary failure");
      
      // Verify retry configuration was used
      expect(mockStep.runAction).toHaveBeenCalledWith(
        internal.workflows.chaining.getFashionAdvice,
        { promptMessageId: mockFashionMessage.messageId, threadId: "thread777" },
        {
          retry: { maxAttempts: 5, initialBackoffMs: 1000, base: 2 }
        }
      );
    });
  });

  describe("Error Boundary Tests", () => {
    it("should handle null/undefined inputs gracefully", async () => {
      // Test getAdvice with null values
      (getAuthUserId as vi.Mock).mockResolvedValue("user123");
      
      await expect(
        getAdvice.handler(mockCtx, { location: null as any, threadId: "thread123" })
      ).rejects.toThrow();
      
      await expect(
        getAdvice.handler(mockCtx, { location: "Boston", threadId: null as any })
      ).rejects.toThrow();
    });

    it("should handle extremely long location strings", async () => {
      // Arrange
      const longLocation = "A".repeat(10000);
      const mockUserId = "user505";
      
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (weatherAgent.generateText as vi.Mock).mockResolvedValue("Weather data");
      (fashionAgent.generateText as vi.Mock).mockResolvedValue("Fashion advice");
      
      // Act & Assert - Should not crash
      await expect(
        getAdvice.handler(mockCtx, { location: longLocation, threadId: "thread888" })
      ).resolves.not.toThrow();
    });

    it("should handle concurrent workflow executions", async () => {
      // Arrange - Multiple simultaneous workflow starts
      const mockUserId = "user606";
      (getAuthUserId as vi.Mock).mockResolvedValue(mockUserId);
      (createThread as vi.Mock)
        .mockResolvedValueOnce("thread001")
        .mockResolvedValueOnce("thread002")
        .mockResolvedValueOnce("thread003");
      mockWorkflow.start
        .mockResolvedValueOnce("workflow001" as WorkflowId)
        .mockResolvedValueOnce("workflow002" as WorkflowId)
        .mockResolvedValueOnce("workflow003" as WorkflowId);
      
      // Act - Start multiple workflows concurrently
      const promises = [
        startWorkflow.handler(mockCtx, { location: "NYC" }),
        startWorkflow.handler(mockCtx, { location: "LA" }),
        startWorkflow.handler(mockCtx, { location: "Chicago" })
      ];
      
      const results = await Promise.all(promises);
      
      // Assert - All should complete successfully
      expect(results).toHaveLength(3);
      expect(results[0].threadId).toBe("thread001");
      expect(results[1].threadId).toBe("thread002");
      expect(results[2].threadId).toBe("thread003");
    });
  });
});