// convex/agents/vaguenessGate.ts
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { v } from "convex/values";

// Agent to check if user input is too vague
export const vaguenessGateAgent = new Agent(components.agent, {
  name: "VaguenessGate",
  chat: openai.chat("gpt-4o-mini"),
  instructions: `You are a vagueness detection agent. Your job is to determine if a user's input is too vague to process.
  
  A request is TOO VAGUE if:
  - It lacks specific context or details
  - It's too broad or generic (e.g., "help me", "make it better")
  - It doesn't clearly state what the user wants
  - It contains ambiguous references without context
  
  A request is CLEAR ENOUGH if:
  - It has a specific goal or outcome
  - It mentions concrete elements or features
  - The intent is understandable even if some details are missing
  
  Be reasonable - not every request needs perfect clarity, just enough to proceed.`,
});

// Schema for vagueness check result
const vaguenessCheckSchema = z.object({
  isVague: z.boolean().describe("Whether the input is too vague"),
  reason: z.string().describe("Explanation of why it's vague or clear"),
  suggestions: z.array(z.string()).describe("Suggestions to make the request clearer").optional(),
});

// Expose as action for workflow
export const checkVagueness = vaguenessGateAgent.asObjectAction({
  schema: vaguenessCheckSchema,
});

// Internal action with custom args
export const analyzeVagueness = vaguenessGateAgent.wrapAction({
  args: { 
    userInput: v.string(),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Analyze this user input for vagueness: "${args.userInput}"`,
      schema: vaguenessCheckSchema,
    });
    
    return {
      ...result.object,
      threadId: args.threadId,
    };
  },
});