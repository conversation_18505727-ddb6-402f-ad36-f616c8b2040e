// convex/agents/responseGen.ts
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { v } from "convex/values";

// Agent to generate various types of responses
export const responseGenAgent = new Agent(components.agent, {
  name: "ResponseGen",
  chat: openai.chat("gpt-4o"),
  instructions: `You are a response generation agent. You create appropriate responses based on the analysis results from other agents.`,
});

// Response type schemas
const rejectionResponseSchema = z.object({
  type: z.literal("rejection"),
  message: z.string().describe("Polite rejection message"),
  reason: z.string().describe("Clear reason for rejection"),
  suggestion: z.string().describe("Helpful suggestion for improvement"),
});

const clarificationResponseSchema = z.object({
  type: z.literal("clarification"),
  message: z.string().describe("Request for clarification"),
  questions: z.array(z.string()).describe("Specific clarification questions"),
  context: z.string().describe("Context about why clarification is needed"),
});

const definitionLockResponseSchema = z.object({
  type: z.literal("definition_lock"),
  message: z.string().describe("Confirmation of locked requirements"),
  summary: z.string().describe("Clear summary of what will be built"),
  specifications: z.array(z.string()).describe("List of locked specifications"),
  nextSteps: z.string().describe("What happens next"),
});

// Generate immediate rejection response
export const generateRejection = responseGenAgent.wrapAction({
  args: { 
    reason: v.string(),
    suggestions: v.optional(v.array(v.string())),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Generate a polite rejection response because: ${args.reason}
      ${args.suggestions ? `Suggestions: ${args.suggestions.join(", ")}` : ""}`,
      schema: rejectionResponseSchema,
    });
    
    return result.object;
  },
});

// Generate binary choice forcing response
export const generateBinaryChoice = responseGenAgent.wrapAction({
  args: { 
    topics: v.array(v.string()),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateText({
      prompt: `The user mentioned multiple topics: ${args.topics.join(", ")}. 
      Generate a response asking them to choose ONE specific topic to focus on first.
      Be friendly but firm about needing to handle one thing at a time.`,
    });
    
    return { type: "binary_choice", message: result.text };
  },
});

// Generate scope rejection response
export const generateScopeRejection = responseGenAgent.wrapAction({
  args: { 
    scopeIssue: v.string(),
    features: v.array(v.string()),
    prioritySuggestions: v.optional(v.array(v.string())),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateText({
      prompt: `The request has scope issues (${args.scopeIssue}). 
      Features identified: ${args.features.join(", ")}
      ${args.prioritySuggestions ? `Priority suggestions: ${args.prioritySuggestions.join(", ")}` : ""}
      Generate a helpful response explaining the scope issue and suggesting a more focused approach.`,
    });
    
    return { type: "scope_rejection", message: result.text };
  },
});

// Generate clarification request
export const generateClarification = responseGenAgent.wrapAction({
  args: { 
    questions: v.array(v.string()),
    round: v.number(),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Generate clarification request (round ${args.round}) for these questions: ${args.questions.join("; ")}`,
      schema: clarificationResponseSchema,
    });
    
    return result.object;
  },
});

// Generate definition lock response
export const generateDefinitionLock = responseGenAgent.wrapAction({
  args: { 
    userInput: v.string(),
    features: v.array(v.string()),
    specifications: v.array(v.string()),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Generate a confirmation response that locks in these requirements:
      Original request: "${args.userInput}"
      Features: ${args.features.join(", ")}
      Specifications: ${args.specifications.join("; ")}`,
      schema: definitionLockResponseSchema,
    });
    
    return result.object;
  },
});

// Generate final response
export const generateFinalResponse = responseGenAgent.wrapAction({
  args: { 
    userInput: v.string(),
    analysis: v.any(),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateText({
      prompt: `Generate the final response for: "${args.userInput}"
      Based on this analysis: ${JSON.stringify(args.analysis)}`,
    });
    
    return { type: "final", message: result.text };
  },
});