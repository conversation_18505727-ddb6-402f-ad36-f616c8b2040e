// convex/agents/focusGate.ts
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { v } from "convex/values";

// Agent to determine if request is about single or multiple products/topics
export const focusGateAgent = new Agent(components.agent, {
  name: "FocusGate",
  chat: openai.chat("gpt-4o-mini"),
  instructions: `You are a focus analysis agent. Your job is to determine if a user's request is focused on a single product/topic or multiple products/topics.
  
  SINGLE FOCUS requests:
  - Ask about one specific product, feature, or topic
  - Have a clear, singular goal
  - May have multiple aspects but all relate to one main thing
  
  MULTI FOCUS requests:
  - Compare multiple products or services
  - Ask about several unrelated topics
  - Request information spanning different domains
  - Want to accomplish multiple distinct goals
  
  When unclear, lean towards SINGLE focus if there's a primary topic with minor related elements.`,
});

// Schema for focus check result
const focusCheckSchema = z.object({
  focusType: z.enum(["single", "multi"]).describe("Whether request has single or multiple focus"),
  primaryTopic: z.string().describe("The main topic or product identified"),
  additionalTopics: z.array(z.string()).describe("Other topics mentioned if multi-focus").optional(),
  confidence: z.number().min(0).max(1).describe("Confidence in the assessment"),
});

// Expose as action for workflow
export const checkFocus = focusGateAgent.asObjectAction({
  schema: focusCheckSchema,
});

// Internal action with custom args
export const analyzeFocus = focusGateAgent.wrapAction({
  args: { 
    userInput: v.string(),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Analyze the focus of this request: "${args.userInput}"`,
      schema: focusCheckSchema,
    });
    
    return {
      ...result.object,
      threadId: args.threadId,
    };
  },
});