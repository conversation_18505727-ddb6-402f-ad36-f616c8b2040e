// convex/agents/refinement.ts
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { v } from "convex/values";

// Agent to check specificity and refine requirements
export const refinementAgent = new Agent(components.agent, {
  name: "Refinement",
  chat: openai.chat("gpt-4o-mini"),
  instructions: `You are a requirements refinement agent. Your job is to check if requirements are specific enough and generate clarifying questions when needed.
  
  SUFFICIENT SPECIFICITY:
  - Clear technical requirements
  - Defined success criteria
  - Specific constraints or parameters
  - Actionable details
  
  NEEDS REFINEMENT:
  - Missing key details
  - Ambiguous technical terms
  - Unclear success metrics
  - Unspecified constraints
  
  Generate targeted clarification questions to gather missing information.`,
});

// Schema for specificity check
const specificityCheckSchema = z.object({
  isSpecific: z.boolean().describe("Whether requirements are specific enough"),
  missingDetails: z.array(z.string()).describe("List of missing or unclear details"),
  clarificationQuestions: z.array(z.string()).describe("Questions to ask for clarification"),
  refinementRound: z.number().describe("Current refinement iteration"),
  confidence: z.number().min(0).max(1).describe("Confidence that we have enough detail"),
});

// Expose as action for workflow
export const checkSpecificity = refinementAgent.asObjectAction({
  schema: specificityCheckSchema,
});

// Internal action for refinement
export const refineRequirements = refinementAgent.wrapAction({
  args: { 
    userInput: v.string(),
    features: v.array(v.string()),
    refinementRound: v.number(),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Check specificity of these requirements (round ${args.refinementRound}):
      Original request: "${args.userInput}"
      Identified features: ${args.features.join(", ")}`,
      schema: specificityCheckSchema,
    });
    
    return {
      ...result.object,
      refinementRound: args.refinementRound,
      threadId: args.threadId,
    };
  },
});