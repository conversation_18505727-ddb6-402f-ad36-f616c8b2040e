// convex/agents/scopeGate.ts
import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { v } from "convex/values";

// Agent to analyze scope and feature requirements
export const scopeGateAgent = new Agent(components.agent, {
  name: "ScopeGate",
  chat: openai.chat("gpt-4o-mini"),
  instructions: `You are a scope analysis agent. Your job is to determine if a user's request has appropriate scope.
  
  APPROPRIATE SCOPE:
  - Clear boundaries on what needs to be done
  - Realistic and achievable goals
  - Specific features or outcomes defined
  - Reasonable complexity for the context
  
  SCOPE ISSUES:
  - "Scope Creep": Request keeps expanding or has unclear boundaries
  - "Kitchen Sink": Trying to include everything possible without prioritization
  - Unrealistic expectations given constraints
  - Too many unrelated features bundled together
  
  Assess whether the scope is manageable and well-defined.`,
});

// Schema for scope analysis
const scopeAnalysisSchema = z.object({
  scopeStatus: z.enum(["appropriate", "scope_creep", "kitchen_sink"]).describe("Assessment of scope"),
  features: z.array(z.string()).describe("List of identified features or requirements"),
  complexity: z.enum(["low", "medium", "high", "excessive"]).describe("Overall complexity assessment"),
  recommendation: z.string().describe("Recommendation for handling the scope"),
  prioritySuggestions: z.array(z.string()).describe("Suggested priority features if scope is too large").optional(),
});

// Expose as action for workflow
export const analyzeScope = scopeGateAgent.asObjectAction({
  schema: scopeAnalysisSchema,
});

// Internal action with custom args
export const checkScope = scopeGateAgent.wrapAction({
  args: { 
    userInput: v.string(),
    focusType: v.string(),
    threadId: v.string(),
  },
  handler: async (ctx, args, thread) => {
    const result = await thread.generateObject({
      prompt: `Analyze the scope of this ${args.focusType}-focus request: "${args.userInput}"`,
      schema: scopeAnalysisSchema,
    });
    
    return {
      ...result.object,
      threadId: args.threadId,
    };
  },
});