// convex/workflow.ts
import { WorkflowManager } from "@convex-dev/workflow";
import { components, internal } from "./_generated/api";
import { v } from "convex/values";
import { mutation, action } from "./_generated/server";

const workflow = new WorkflowManager(components.workflow);

// Declare the response type before the workflow definition
type WorkflowResponse = {
  message: string;
  // Add other fields based on your response structure
};

export const hierarchicalCascadeWorkflow = workflow.define({
  args: {
    userInput: v.string(),
    userId: v.string(),
    maxRefinementRounds: v.optional(v.number()),
  },
  handler: async (step, args): Promise<{
    status: string;
    response: WorkflowResponse;
    threadIds: Record<string, string>;
  }> => {
    const maxRounds = args.maxRefinementRounds ?? 3;
    const threadIds: Record<string, string> = {};

    // Step 1: Vagueness Gate
    const vaguenessResult = await step.runAction(
      internal.agents.vaguenessGate.analyzeVagueness,
      {
        userInput: args.userInput,
        threadId: await createAgentThread(step, "vagueness", args.userId),
      },
      { retry: { maxAttempts: 3 } }
    );
    threadIds.vagueness = vaguenessResult.threadId;

    if (vaguenessResult.isVague) {
      // Generate immediate rejection response
      const rejection = await step.runAction(
        internal.agents.responseGen.generateRejection,
        {
          reason: vaguenessResult.reason,
          suggestions: vaguenessResult.suggestions,
          threadId: await createAgentThread(step, "response", args.userId),
        }
      );
      return {
        status: "rejected_vague",
        response: rejection,
        threadIds
      };
    }

    // Step 2: Focus Gate (Single vs Multi-Product)
    const focusResult = await step.runAction(
      internal.agents.focusGate.analyzeFocus,
      {
        userInput: args.userInput,
        threadId: await createAgentThread(step, "focus", args.userId),
      },
      { retry: { maxAttempts: 3 } }
    );
    threadIds.focus = focusResult.threadId;

    if (focusResult.focusType === "multi" && focusResult.additionalTopics) {
      // Generate binary choice forcing response
      const binaryChoice = await step.runAction(
        internal.agents.responseGen.generateBinaryChoice,
        {
          topics: [focusResult.primaryTopic, ...focusResult.additionalTopics],
          threadId: await createAgentThread(step, "response", args.userId),
        }
      );
      return {
        status: "needs_focus",
        response: binaryChoice,
        threadIds
      };
    }

    // Step 3: Scope Gate (Feature Analysis)
    const scopeResult = await step.runAction(
      internal.agents.scopeGate.checkScope,
      {
        userInput: args.userInput,
        focusType: focusResult.focusType,
        threadId: await createAgentThread(step, "scope", args.userId),
      },
      { retry: { maxAttempts: 3 } }
    );
    threadIds.scope = scopeResult.threadId;

    if (scopeResult.scopeStatus !== "appropriate") {
      // Generate scope rejection response
      const scopeRejection = await step.runAction(
        internal.agents.responseGen.generateScopeRejection,
        {
          scopeIssue: scopeResult.scopeStatus,
          features: scopeResult.features,
          prioritySuggestions: scopeResult.prioritySuggestions,
          threadId: await createAgentThread(step, "response", args.userId),
        }
      );
      return {
        status: `rejected_${scopeResult.scopeStatus}`,
        response: scopeRejection,
        threadIds
      };
    }

    // Step 4: Refinement Loop
    let refinementRound = 1;
    const currentSpecs = scopeResult.features;

    while (refinementRound <= maxRounds) {
      const refinementResult = await step.runAction(
        internal.agents.refinement.refineRequirements,
        {
          userInput: args.userInput,
          features: currentSpecs,
          refinementRound,
          threadId: await createAgentThread(step, `refinement_${refinementRound}`, args.userId),
        },
        { retry: { maxAttempts: 3 } }
      );
      threadIds[`refinement_${refinementRound}`] = refinementResult.threadId;

      if (refinementResult.isSpecific || refinementResult.confidence > 0.8) {
        // Generate definition lock response
        const definitionLock = await step.runAction(
          internal.agents.responseGen.generateDefinitionLock,
          {
            userInput: args.userInput,
            features: currentSpecs,
            specifications: refinementResult.missingDetails.length === 0
              ? currentSpecs
              : [...currentSpecs, ...refinementResult.missingDetails],
            threadId: await createAgentThread(step, "response", args.userId),
          }
        );
        return {
          status: "definition_locked",
          response: definitionLock,
          threadIds
        };
      } else {
        // Generate clarification request
        const clarification = await step.runAction(
          internal.agents.responseGen.generateClarification,
          {
            questions: refinementResult.clarificationQuestions,
            round: refinementRound,
            threadId: await createAgentThread(step, "response", args.userId),
          }
        );

        // In a real implementation, you would wait for user response here
        // For this example, we'll simulate moving to the next round
        refinementRound++;

        if (refinementRound > maxRounds) {
          return {
            status: "needs_more_clarification",
            response: clarification,
            threadIds
          };
        }
      }
    }

    // Step 5: Generate final response
    const finalResponse = await step.runAction(
      internal.agents.responseGen.generateFinalResponse,
      {
        userInput: args.userInput,
        analysis: {
          focus: focusResult,
          scope: scopeResult,
          refinements: refinementRound,
        },
        threadId: await createAgentThread(step, "response", args.userId),
      }
    );
    threadIds.response = finalResponse.threadId;

    return {
      status: "completed",
      response: finalResponse,
      threadIds
    };
  },
});

// Helper to create agent threads
import type { WorkflowStep } from "@convex-dev/workflow";

async function createAgentThread(
  step: WorkflowStep<typeof internal.workflow.createThread>,
  agentType: string,
  userId: string
): Promise<string> {
  const result = await step.runMutation(internal.workflow.createThread, {
    userId,
    title: `${agentType} analysis`,
  });
  return result.threadId;
}

// Mutation to create threads
export const createThread = mutation({
  args: { userId: v.string(), title: v.string() },
  handler: async (ctx, args): Promise<{ threadId: string }> => {
    const threadId = await ctx.db.insert("threads", {
      userId: args.userId,
      title: args.title,
      createdAt: Date.now(),
    });
    return { threadId };
  },
});

// Main entry point to start the workflow
export const processUserInput = action({
  args: {
    userInput: v.string(),
    userId: v.string(),
  },
  handler: async (ctx, args): Promise<{
    workflowId: string;
    status: string;
  }> => {
    const workflowId = await workflow.start(
      ctx,
      internal.workflow.hierarchicalCascadeWorkflow,
      {
        userInput: args.userInput,
        userId: args.userId,
      }
    );

    return {
      workflowId,
      status: "processing",
    };
  },
});

// Query to get workflow status
export const getWorkflowStatus = workflow.statusQuery();