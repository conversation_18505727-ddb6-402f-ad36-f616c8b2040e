import { vi } from 'vitest'
import { Message } from '../../types'

export const mockMessages: Message[] = [
  {
    id: '1',
    role: 'user',
    content: 'I want to build a social media app for pet owners',
    timestamp: Date.now(),
  },
  {
    id: '2',
    role: 'assistant',
    content: 'That\'s a great idea! Let me help you create a comprehensive PRD for your pet social media app...',
    timestamp: Date.now(),
  },
  {
    id: '3',
    role: 'user',
    content: 'Can you add features for pet health tracking?',
    timestamp: Date.now(),
  },
]

export const mockLongMessage: Message = {
  id: '4',
  role: 'assistant',
  content: 'A'.repeat(5000), // Long message for testing
  timestamp: Date.now(),
}

export const mockEmptyMessages: Message[] = []

export const mockLoadingState = {
  messages: mockMessages,
  input: '',
  handleInputChange: vi.fn(),
  handleSubmit: vi.fn(),
  isLoading: true,
  isStreaming: false, // Not streaming, just loading
  isError: false,
  canCancel: false,
  error: null,
  currentStreamingMessage: null,
  conversationId: 'test-conversation-id' as any,
  cancelStream: vi.fn(),
  retry: vi.fn(),
}

export const mockErrorState = {
  messages: mockMessages,
  input: '',
  handleInputChange: vi.fn(),
  handleSubmit: vi.fn(),
  isLoading: false,
  isStreaming: false,
  isError: true,
  canCancel: false,
  error: 'API Error',
  currentStreamingMessage: null,
  conversationId: 'test-conversation-id' as any,
  cancelStream: vi.fn(),
  retry: vi.fn(),
}

export const mockDefaultState = {
  messages: mockMessages,
  input: 'Test input',
  handleInputChange: vi.fn(),
  handleSubmit: vi.fn(),
  isLoading: false,
  isStreaming: false,
  isError: false,
  canCancel: false,
  error: null,
  currentStreamingMessage: null,
  conversationId: 'test-conversation-id' as any,
  cancelStream: vi.fn(),
  retry: vi.fn(),
}
