import {
  CLARIFICATION_MODE_PROMPT,
  PRD_GENERATION_PROMPT,
  PRD_REFINEMENT_PROMPT,
  SYSTEM_PROMPT_SELECTOR,
  DEFAULT_SYSTEM_PROMPT
} from '../../lib/prompts'

describe('Prompts Library', () => {
  describe('Prompt Constants', () => {
    it('exports CLARIFICATION_MODE_PROMPT as a non-empty string', () => {
      expect(typeof CLARIFICATION_MODE_PROMPT).toBe('string')
      expect(CLARIFICATION_MODE_PROMPT.length).toBeGreaterThan(0)
      expect(CLARIFICATION_MODE_PROMPT).toContain('PRDGeneral')
      expect(CLARIFICATION_MODE_PROMPT).toContain('clarification engine')
    })

    it('exports PRD_GENERATION_PROMPT as a non-empty string', () => {
      expect(typeof PRD_GENERATION_PROMPT).toBe('string')
      expect(PRD_GENERATION_PROMPT.length).toBe<PERSON>reater<PERSON>han(0)
      expect(PRD_GENERATION_PROMPT).toContain('PRD Generation Mode')
      expect(PRD_GENERATION_PROMPT).toContain('Executive Summary')
    })

    it('exports PRD_REFINEMENT_PROMPT as a non-empty string', () => {
      expect(typeof PRD_REFINEMENT_PROMPT).toBe('string')
      expect(PRD_REFINEMENT_PROMPT.length).toBeGreaterThan(0)
      expect(PRD_REFINEMENT_PROMPT).toContain('PRD Refinement Mode')
      expect(PRD_REFINEMENT_PROMPT).toContain('refine and improve')
    })

    it('sets DEFAULT_SYSTEM_PROMPT to CLARIFICATION_MODE_PROMPT', () => {
      expect(DEFAULT_SYSTEM_PROMPT).toBe(CLARIFICATION_MODE_PROMPT)
    })
  })

  describe('SYSTEM_PROMPT_SELECTOR Function', () => {
    it('returns PRD_GENERATION_PROMPT for generation mode', () => {
      const result = SYSTEM_PROMPT_SELECTOR('generation')
      expect(result).toBe(PRD_GENERATION_PROMPT)
    })

    it('returns PRD_REFINEMENT_PROMPT for refinement mode', () => {
      const result = SYSTEM_PROMPT_SELECTOR('refinement')
      expect(result).toBe(PRD_REFINEMENT_PROMPT)
    })

    it('returns CLARIFICATION_MODE_PROMPT for clarification mode', () => {
      const result = SYSTEM_PROMPT_SELECTOR('clarification')
      expect(result).toBe(CLARIFICATION_MODE_PROMPT)
    })

    it('returns CLARIFICATION_MODE_PROMPT for invalid mode', () => {
      // @ts-expect-error Testing invalid input
      const result = SYSTEM_PROMPT_SELECTOR('invalid')
      expect(result).toBe(CLARIFICATION_MODE_PROMPT)
    })

    it('returns CLARIFICATION_MODE_PROMPT for undefined mode', () => {
      // @ts-expect-error Testing undefined input
      const result = SYSTEM_PROMPT_SELECTOR(undefined)
      expect(result).toBe(CLARIFICATION_MODE_PROMPT)
    })

    it('returns CLARIFICATION_MODE_PROMPT for null mode', () => {
      // @ts-expect-error Testing null input
      const result = SYSTEM_PROMPT_SELECTOR(null)
      expect(result).toBe(CLARIFICATION_MODE_PROMPT)
    })
  })

  describe('Prompt Content Validation', () => {
    it('CLARIFICATION_MODE_PROMPT contains key sections', () => {
      expect(CLARIFICATION_MODE_PROMPT).toContain('Core Philosophy')
      expect(CLARIFICATION_MODE_PROMPT).toContain('Your Personality')
      expect(CLARIFICATION_MODE_PROMPT).toContain('Your Mission')
      expect(CLARIFICATION_MODE_PROMPT).toContain('Step 1: Specific Problem Definition')
      expect(CLARIFICATION_MODE_PROMPT).toContain('Step 2: Core Feature Identification')
      expect(CLARIFICATION_MODE_PROMPT).toContain('Step 3: Target User Clarity')
      expect(CLARIFICATION_MODE_PROMPT).toContain('Step 4: Success Metrics Definition')
    })

    it('PRD_GENERATION_PROMPT contains required sections', () => {
      expect(PRD_GENERATION_PROMPT).toContain('Executive Summary')
      expect(PRD_GENERATION_PROMPT).toContain('Problem Statement')
      expect(PRD_GENERATION_PROMPT).toContain('Solution Overview')
      expect(PRD_GENERATION_PROMPT).toContain('Target User Profile')
      expect(PRD_GENERATION_PROMPT).toContain('Success Metrics')
      expect(PRD_GENERATION_PROMPT).toContain('Technical Requirements')
      expect(PRD_GENERATION_PROMPT).toContain('Launch Strategy')
      expect(PRD_GENERATION_PROMPT).toContain('Risk Assessment')
      expect(PRD_GENERATION_PROMPT).toContain('800 words maximum')
    })

    it('PRD_REFINEMENT_PROMPT contains refinement guidance', () => {
      expect(PRD_REFINEMENT_PROMPT).toContain('Your Role')
      expect(PRD_REFINEMENT_PROMPT).toContain('Refinement Approach')
      expect(PRD_REFINEMENT_PROMPT).toContain('What You Can Refine')
      expect(PRD_REFINEMENT_PROMPT).toContain('Refinement Guidelines')
      expect(PRD_REFINEMENT_PROMPT).toContain('Response Pattern')
    })
  })

  describe('Prompt Consistency', () => {
    it('all prompts mention Solo Entrepreneur Framework or similar concepts', () => {
      expect(PRD_GENERATION_PROMPT).toContain('Solo Entrepreneur Framework')
      expect(PRD_REFINEMENT_PROMPT).toContain('Solo Entrepreneur Framework')
      // Clarification mode focuses on the process leading to PRD generation
    })

    it('all prompts maintain consistent word limits', () => {
      expect(PRD_GENERATION_PROMPT).toContain('800 words')
      expect(PRD_REFINEMENT_PROMPT).toContain('800-word limit')
    })

    it('all prompts emphasize single-purpose philosophy', () => {
      expect(CLARIFICATION_MODE_PROMPT).toContain('Single-purpose obsession')
      expect(PRD_GENERATION_PROMPT).toContain('single-purpose philosophy')
      expect(PRD_REFINEMENT_PROMPT).toContain('single-purpose focus')
    })
  })
})
