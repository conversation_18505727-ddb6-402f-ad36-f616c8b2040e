import { render, screen } from '../utils/test-utils'
import userEvent from '@testing-library/user-event'
import ChatInput from '../../components/ChatInput'
import { vi, describe, beforeEach, it, expect } from 'vitest'

describe('ChatInput', () => {
  const defaultProps = {
    input: '',
    handleInputChange: vi.fn(),
    handleSubmit: vi.fn(),
    isLoading: false,
    messageCount: 0,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders the input form with correct elements', () => {
      render(<ChatInput {...defaultProps} />)
      
      expect(screen.getByRole('form')).toBeInTheDocument()
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /send message/i })).toBeInTheDocument()
      expect(screen.getByText(/characters remaining/i)).toBeInTheDocument()
    })

    it('displays correct placeholder text', () => {
      render(<ChatInput {...defaultProps} />)
      
      expect(screen.getByPlaceholderText(/describe your product idea/i)).toBeInTheDocument()
    })

    it('shows character count correctly', () => {
      render(<ChatInput {...defaultProps} input="Hello" />)
      
      expect(screen.getByText('9,995 characters remaining')).toBeInTheDocument()
    })
  })

  describe('Form Submission', () => {
    it('calls handleSubmit when form is submitted', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="Test message" />)
      
      const submitButton = screen.getByRole('button', { name: /send message/i })
      await user.click(submitButton)
      
      expect(defaultProps.handleSubmit).toHaveBeenCalledTimes(1)
    })

    it('prevents submission when input is empty', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="" />)
      
      const submitButton = screen.getByRole('button', { name: /send message/i })
      expect(submitButton).toBeDisabled()
      
      await user.click(submitButton)
      expect(defaultProps.handleSubmit).not.toHaveBeenCalled()
    })

    it('prevents submission when input is only whitespace', async () => {
      render(<ChatInput {...defaultProps} input="   " />)

      const submitButton = screen.getByRole('button', { name: /send message/i })
      expect(submitButton).toBeDisabled()
    })

    it('prevents submission when loading', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="Test" isLoading={true} />)
      
      const submitButton = screen.getByRole('button', { name: /sending message/i })
      expect(submitButton).toBeDisabled()
      
      await user.click(submitButton)
      expect(defaultProps.handleSubmit).not.toHaveBeenCalled()
    })
  })

  describe('Keyboard Shortcuts', () => {
    it('submits form when Enter is pressed', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="Test message" />)
      
      const textarea = screen.getByRole('textbox')
      await user.click(textarea)
      await user.keyboard('{Enter}')
      
      expect(defaultProps.handleSubmit).toHaveBeenCalledTimes(1)
    })

    it('does not submit when Shift+Enter is pressed', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="Test message" />)
      
      const textarea = screen.getByRole('textbox')
      await user.click(textarea)
      await user.keyboard('{Shift>}{Enter}{/Shift}')
      
      expect(defaultProps.handleSubmit).not.toHaveBeenCalled()
    })

    it('does not submit on Enter when input is empty', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="" />)
      
      const textarea = screen.getByRole('textbox')
      await user.click(textarea)
      await user.keyboard('{Enter}')
      
      expect(defaultProps.handleSubmit).not.toHaveBeenCalled()
    })

    it('does not submit on Enter when loading', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} input="Test" isLoading={true} />)
      
      const textarea = screen.getByRole('textbox')
      await user.click(textarea)
      await user.keyboard('{Enter}')
      
      expect(defaultProps.handleSubmit).not.toHaveBeenCalled()
    })
  })

  describe('Input Validation', () => {
    it('shows validation error when input exceeds character limit', () => {
      const longInput = 'a'.repeat(10001)
      render(<ChatInput {...defaultProps} input={longInput} />)
      
      expect(screen.getByText(/message too long/i)).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeDisabled()
    })

    it('shows warning when approaching character limit', () => {
      const nearLimitInput = 'a'.repeat(9500)
      render(<ChatInput {...defaultProps} input={nearLimitInput} />)
      
      const remainingText = screen.getByText(/characters remaining/i)
      expect(remainingText).toHaveClass('text-orange-500')
    })

    it('updates character count in real-time', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} />)
      
      const textarea = screen.getByRole('textbox')
      await user.type(textarea, 'Hello')
      
      expect(defaultProps.handleInputChange).toHaveBeenCalled()
    })
  })

  describe('Loading States', () => {
    it('shows loading spinner when isLoading is true', () => {
      render(<ChatInput {...defaultProps} isLoading={true} />)
      
      expect(screen.getByRole('button', { name: /sending message/i })).toBeInTheDocument()
      expect(screen.getByRole('textbox')).toBeDisabled()
    })

    it('disables input when loading', () => {
      render(<ChatInput {...defaultProps} isLoading={true} />)
      
      expect(screen.getByRole('textbox')).toBeDisabled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<ChatInput {...defaultProps} />)
      
      expect(screen.getByLabelText(/enter your product idea/i)).toBeInTheDocument()
      expect(screen.getByRole('form')).toHaveAttribute('aria-label', 'Chat message form')
    })

    it('has screen reader support for character count', () => {
      render(<ChatInput {...defaultProps} />)
      
      expect(screen.getByText(/press enter to send/i)).toHaveClass('sr-only')
    })

    it('shows validation errors with proper ARIA attributes', () => {
      const longInput = 'a'.repeat(10001)
      render(<ChatInput {...defaultProps} input={longInput} />)
      
      const errorMessage = screen.getByRole('alert')
      expect(errorMessage).toHaveAttribute('aria-live', 'polite')
    })
  })

  describe('Auto-resize Functionality', () => {
    it('auto-resizes textarea based on content', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} />)

      const textarea = screen.getByRole('textbox') as HTMLTextAreaElement

      // Mock scrollHeight to simulate content growth
      Object.defineProperty(textarea, 'scrollHeight', {
        value: 100,
        writable: true,
        configurable: true,
      })

      await user.type(textarea, 'Multi\nline\ntext')

      // In test environment, we verify the textarea has the right attributes
      // The actual height setting happens in useEffect which may not trigger in jsdom
      expect(textarea).toHaveAttribute('rows', '1')
      expect(textarea.className).toContain('resize-none')
    })

    it('respects maximum height limit', async () => {
      const user = userEvent.setup()
      render(<ChatInput {...defaultProps} />)

      const textarea = screen.getByRole('textbox') as HTMLTextAreaElement

      await user.type(textarea, 'Very long text')

      // Verify the textarea has max-height constraint in CSS
      expect(textarea.className).toContain('max-h-[120px]')
      expect(textarea.className).toContain('resize-none')
    })
  })
})
