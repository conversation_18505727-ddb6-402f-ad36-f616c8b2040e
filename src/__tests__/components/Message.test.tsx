import { render, screen } from '../utils/test-utils'
import Message from '../../components/Message'
import { Message as MessageType } from 'ai'

describe('Message', () => {
  const userMessage: MessageType = {
    id: '1',
    role: 'user',
    content: 'I want to build a social media app',
  }

  const assistantMessage: MessageType = {
    id: '2',
    role: 'assistant',
    content: 'That\'s a great idea! Let me help you create a comprehensive PRD...',
  }

  const longMessage: MessageType = {
    id: '3',
    role: 'assistant',
    content: 'This is a very long message that should test how the component handles extensive content. '.repeat(50),
  }

  describe('Rendering', () => {
    it('renders user message correctly', () => {
      render(<Message message={userMessage} />)

      expect(screen.getByText(userMessage.content)).toBeInTheDocument()
      expect(screen.getByRole('article')).toHaveAttribute('aria-label', expect.stringContaining('User message'))
    })

    it('renders assistant message correctly', () => {
      render(<Message message={assistantMessage} />)

      expect(screen.getByText(assistantMessage.content)).toBeInTheDocument()
      expect(screen.getByRole('article')).toHaveAttribute('aria-label', expect.stringContaining('Assistant message'))
    })

    it('applies correct styling for user messages', () => {
      render(<Message message={userMessage} />)

      const messageContainer = screen.getByText(userMessage.content).closest('div')?.parentElement
      expect(messageContainer).toHaveClass('bg-blue-500', 'text-white')
    })

    it('applies correct styling for assistant messages', () => {
      render(<Message message={assistantMessage} />)

      const messageContainer = screen.getByText(assistantMessage.content).closest('div')?.parentElement
      expect(messageContainer).not.toBeNull()
      expect(messageContainer!).toHaveClass('bg-gray-200', 'dark:bg-gray-700')
    })
  })

  describe('Message Layout', () => {
    it('positions user messages on the right', () => {
      render(<Message message={userMessage} />)

      const messageWrapper = screen.getByRole('article')
      expect(messageWrapper).toHaveClass('justify-end')
    })

    it('positions assistant messages on the left', () => {
      render(<Message message={assistantMessage} />)

      const messageWrapper = screen.getByRole('article')
      expect(messageWrapper).toHaveClass('justify-start')
    })

    it('applies correct styling for user messages', () => {
      render(<Message message={userMessage} />)

      const messageContainer = screen.getByText(userMessage.content).closest('div')?.parentElement
      expect(messageContainer).toHaveClass('bg-blue-500', 'text-white')
    })

    it('applies correct styling for assistant messages', () => {
      render(<Message message={assistantMessage} />)

      const messageContainer = screen.getByText(assistantMessage.content).closest('div')?.parentElement
      expect(messageContainer).toHaveClass('bg-gray-200', 'dark:bg-gray-700')
    })
  })

  describe('Content Handling', () => {
    it('handles long messages correctly', () => {
      render(<Message message={longMessage} />)

      // Use partial text match for very long content
      expect(screen.getByText(/This is a very long message/)).toBeInTheDocument()

      const messageContent = screen.getByText(/This is a very long message/)
      expect(messageContent).toHaveClass('whitespace-pre-wrap')
    })

    it('preserves whitespace and line breaks', () => {
      const messageWithBreaks: MessageType = {
        id: '5',
        role: 'assistant',
        content: 'Line 1\n\nLine 3 after empty line',
      }

      render(<Message message={messageWithBreaks} />)

      // Use partial match for content with line breaks
      const messageContent = screen.getByText(/Line 1/)
      expect(messageContent).toHaveClass('whitespace-pre-wrap')
    })

    it('handles empty content gracefully', () => {
      const emptyMessage: MessageType = {
        id: '6',
        role: 'user',
        content: '',
      }

      render(<Message message={emptyMessage} />)

      expect(screen.getByRole('article')).toBeInTheDocument()
      // Should still render the message structure even with empty content
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes for user messages', () => {
      render(<Message message={userMessage} />)
      
      const messageElement = screen.getByRole('article')
      expect(messageElement).toHaveAttribute('aria-label', 'User message')
    })

    it('has proper ARIA attributes for assistant messages', () => {
      render(<Message message={assistantMessage} />)
      
      const messageElement = screen.getByRole('article')
      expect(messageElement).toHaveAttribute('aria-label', 'Assistant message')
    })

    it('has proper semantic structure', () => {
      render(<Message message={userMessage} />)

      expect(screen.getByRole('article')).toBeInTheDocument()
      expect(screen.getByRole('article')).toHaveAttribute('data-message-id', userMessage.id)
    })

    it('provides screen reader friendly content', () => {
      render(<Message message={assistantMessage} />)
      
      const messageElement = screen.getByRole('article')
      expect(messageElement).toHaveAttribute('aria-label', 'Assistant message')
    })
  })

  describe('Responsive Design', () => {
    it('applies responsive classes correctly', () => {
      render(<Message message={userMessage} />)

      const messageContent = screen.getByText(userMessage.content)
      const messageContainer = messageContent.closest('div')?.parentElement
      expect(messageContainer).toHaveClass('max-w-[85%]', 'sm:max-w-xs', 'lg:max-w-md')
    })

    it('handles different screen sizes for message width', () => {
      render(<Message message={longMessage} />)

      const messageContent = screen.getByText(/This is a very long message/)
      const messageContainer = messageContent.closest('div')?.parentElement
      expect(messageContainer).toHaveClass('max-w-[85%]', 'sm:max-w-xs', 'lg:max-w-md')
    })
  })

  describe('Message Metadata', () => {
    it('displays timestamp when createdAt is provided', () => {
      const messageWithTimestamp: MessageType = {
        id: '1',
        role: 'user',
        content: 'Test message',
        createdAt: new Date('2023-01-01T12:30:00Z')
      }

      render(<Message message={messageWithTimestamp} />)

      // Should display the formatted timestamp (format depends on locale, so check for time pattern)
      expect(screen.getByText(/\d{1,2}:\d{2}/)).toBeInTheDocument()

      // Should have proper aria-label
      const timestampElement = screen.getByLabelText(/Message sent at/)
      expect(timestampElement).toBeInTheDocument()
    })

    it('does not display timestamp when createdAt is missing', () => {
      const messageWithoutTimestamp: MessageType = {
        id: '1',
        role: 'user',
        content: 'Test message'
        // no createdAt
      }

      render(<Message message={messageWithoutTimestamp} />)

      // Should not display any timestamp
      expect(screen.queryByLabelText(/Message sent at/)).not.toBeInTheDocument()

      // Aria-label should not include timestamp
      const messageElement = screen.getByRole('article')
      expect(messageElement).toHaveAttribute('aria-label', 'User message')
    })

    it('includes timestamp in aria-label when available', () => {
      const messageWithTimestamp: MessageType = {
        id: '1',
        role: 'assistant',
        content: 'Test response',
        createdAt: new Date('2023-01-01T15:45:00Z')
      }

      render(<Message message={messageWithTimestamp} />)

      const messageElement = screen.getByRole('article')
      // Check that aria-label contains the message type and time pattern
      expect(messageElement.getAttribute('aria-label')).toMatch(/Assistant message at \d{1,2}:\d{2}/)
    })

    it('applies correct timestamp styling for user messages', () => {
      const userMessageWithTimestamp: MessageType = {
        id: '1',
        role: 'user',
        content: 'User message',
        createdAt: new Date('2023-01-01T10:15:00Z')
      }

      render(<Message message={userMessageWithTimestamp} />)

      const timestampElement = screen.getByLabelText(/Message sent at/)
      expect(timestampElement).toHaveClass('text-blue-100')
    })

    it('applies correct timestamp styling for assistant messages', () => {
      const assistantMessageWithTimestamp: MessageType = {
        id: '1',
        role: 'assistant',
        content: 'Assistant message',
        createdAt: new Date('2023-01-01T14:20:00Z')
      }

      render(<Message message={assistantMessageWithTimestamp} />)

      const timestampElement = screen.getByLabelText(/Message sent at/)
      expect(timestampElement).toHaveClass('text-gray-500', 'dark:text-gray-400')
    })

    it('handles message ID correctly', () => {
      render(<Message message={userMessage} />)

      const messageElement = screen.getByRole('article')
      expect(messageElement).toHaveAttribute('data-message-id', userMessage.id)
    })

    it('formats timestamp correctly for different times', () => {
      const morningMessage: MessageType = {
        id: '1',
        role: 'user',
        content: 'Morning message',
        createdAt: new Date('2023-01-01T09:05:00Z')
      }

      render(<Message message={morningMessage} />)
      // Check for time pattern instead of exact format
      expect(screen.getByText(/\d{1,2}:\d{2}/)).toBeInTheDocument()
    })
  })

  describe('Dark Mode Support', () => {
    it('includes dark mode classes for assistant messages', () => {
      render(<Message message={assistantMessage} />)

      const messageContent = screen.getByText(assistantMessage.content)
      expect(messageContent.closest('div')?.parentElement).toHaveClass('dark:bg-gray-700', 'dark:text-white')
    })

    it('includes dark mode classes for text', () => {
      render(<Message message={assistantMessage} />)

      const messageContent = screen.getByText(assistantMessage.content)
      expect(messageContent.closest('div')?.parentElement).toHaveClass('dark:text-white')
    })
  })

  describe('Error Handling', () => {
    it('handles missing message properties gracefully', () => {
      const invalidMessage = {
        id: '7',
        role: 'user',
        // missing content
      } as MessageType

      render(<Message message={invalidMessage} />)

      expect(screen.getByRole('article')).toBeInTheDocument()
      // Should not crash even with missing content
    })

    it('handles unknown message roles', () => {
      const unknownRoleMessage = {
        id: '8',
        role: 'system' as MessageType['role'],
        content: 'System message',
      }
      
      render(<Message message={unknownRoleMessage} />)
      
      // Should render without crashing, likely defaulting to assistant styling
      expect(screen.getByText('System message')).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('renders quickly with large content', () => {
      const startTime = performance.now()
      render(<Message message={longMessage} />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(50) // Should render in less than 50ms
      expect(screen.getByText(/This is a very long message/)).toBeInTheDocument()
    })
  })
})
