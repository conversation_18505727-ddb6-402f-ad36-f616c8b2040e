import { render, screen } from '../utils/test-utils'
import userEvent from '@testing-library/user-event'
import Chat from '../../components/Chat'
import { mockDefaultState, mockLoadingState, mockErrorState } from '../utils/mock-data'
import { vi, describe, beforeEach, it, expect } from 'vitest'

// Mock the useStreamingChat hook
vi.mock('../../hooks/useStreamingChat', () => ({
  useStreamingChat: vi.fn(),
}))

import { useStreamingChat } from '../../hooks/useStreamingChat'
const mockUseStreamingChat = useStreamingChat as any

describe('Chat Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockUseStreamingChat.mockReturnValue(mockDefaultState as any)
  })

  describe('Component Integration', () => {
    it('renders all child components correctly', () => {
      render(<Chat />)
      
      // Header
      expect(screen.getByText('PRDGeneral')).toBeInTheDocument()
      expect(screen.getByText(/transform your product ideas/i)).toBeInTheDocument()
      
      // Message list
      expect(screen.getByRole('log')).toBeInTheDocument()
      
      // Chat input
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      // Note: Submit button only appears when there's input text
    })

    it('passes correct props to child components', () => {
      render(<Chat />)
      
      // Input should receive the correct props
      const textarea = screen.getByRole('textbox')
      expect(textarea).toHaveValue(mockDefaultState.input)
      
      // Input should have the correct value
      expect(screen.getByRole('textbox')).toHaveValue(mockDefaultState.input)
    })

    it('handles error boundaries correctly', () => {
      // Mock a component that throws an error
      const originalError = console.error
      console.error = vi.fn()

      // This would be tested by mocking a child component to throw
      render(<Chat />)

      // Should still render the main structure
      expect(screen.getByText('PRDGeneral')).toBeInTheDocument()

      console.error = originalError
    })
  })

  describe('Chat Flow Integration', () => {
    it('handles complete message sending flow', async () => {
      const user = userEvent.setup()
      const mockHandleSubmit = vi.fn()
      const mockHandleInputChange = vi.fn()

      mockUseStreamingChat.mockReturnValue({
        ...mockDefaultState,
        handleSubmit: mockHandleSubmit,
        handleInputChange: mockHandleInputChange,
        input: 'Test message',
      } as any)
      
      render(<Chat />)

      const textarea = screen.getByRole('textbox')

      // Type in textarea
      await user.type(textarea, 'New message')
      expect(mockHandleInputChange).toHaveBeenCalled()

      // Submit form by pressing Enter
      await user.keyboard('{Enter}')
      expect(mockHandleSubmit).toHaveBeenCalled()
    })

    it('handles keyboard shortcuts in integration', async () => {
      const user = userEvent.setup()
      const mockHandleSubmit = vi.fn()

      mockUseStreamingChat.mockReturnValue({
        ...mockDefaultState,
        handleSubmit: mockHandleSubmit,
        input: 'Test message',
      } as any)
      
      render(<Chat />)
      
      const textarea = screen.getByRole('textbox')
      await user.click(textarea)
      await user.keyboard('{Enter}')
      
      expect(mockHandleSubmit).toHaveBeenCalled()
    })

    it('prevents submission when input is empty', async () => {
      const user = userEvent.setup()
      const mockHandleSubmit = vi.fn()

      mockUseStreamingChat.mockReturnValue({
        ...mockDefaultState,
        handleSubmit: mockHandleSubmit,
        input: '',
      } as any)

      render(<Chat />)

      // When input is empty, the submit button should be disabled
      const submitButton = screen.getByRole('button', { name: /send message/i })
      expect(submitButton).toBeDisabled()

      // Clicking the disabled button should not trigger submission
      await user.click(submitButton)
      expect(mockHandleSubmit).not.toHaveBeenCalled()
    })
  })

  describe('Loading States Integration', () => {
    it('shows loading state across all components', () => {
      // Create a streaming state with cancel capability
      const streamingState = {
        ...mockLoadingState,
        isStreaming: true,
        canCancel: true,
        cancelStream: vi.fn(),
      }
      mockUseStreamingChat.mockReturnValue(streamingState as any)

      render(<Chat />)

      // Input should be disabled
      expect(screen.getByRole('textbox')).toBeDisabled()

      // Should show stop generation button when streaming with cancel
      expect(screen.getByRole('button', { name: /stop generation/i })).toBeInTheDocument()

      // Should show streaming indicators
      expect(screen.getByText(/AI is responding/i)).toBeInTheDocument()
      expect(screen.getByText(/Streaming/i)).toBeInTheDocument()
    })

    it('handles loading state transitions', () => {
      const { rerender } = render(<Chat />)

      // Start with loading (non-streaming)
      mockUseStreamingChat.mockReturnValue(mockLoadingState as any)
      rerender(<Chat />)

      expect(screen.getByText(/PRDGeneral is thinking/i)).toBeInTheDocument()

      // Switch to normal state
      mockUseStreamingChat.mockReturnValue(mockDefaultState as any)
      rerender(<Chat />)

      expect(screen.queryByText(/PRDGeneral is thinking/i)).not.toBeInTheDocument()
    })
  })

  describe('Error Handling Integration', () => {
    it('handles API errors gracefully', () => {
      mockUseStreamingChat.mockReturnValue(mockErrorState as any)

      render(<Chat />)

      // Should still render the interface
      expect(screen.getByText('PRDGeneral')).toBeInTheDocument()
      expect(screen.getByRole('textbox')).toBeInTheDocument()

      // Error should be handled by error boundaries or displayed appropriately
      // The exact error display depends on how the useStreamingChat hook exposes errors
    })

    it('recovers from errors', () => {
      const { rerender } = render(<Chat />)

      // Start with error state
      mockUseStreamingChat.mockReturnValue(mockErrorState as any)
      rerender(<Chat />)

      // Switch back to normal state
      mockUseStreamingChat.mockReturnValue(mockDefaultState as any)
      rerender(<Chat />)

      // Should be back to normal
      expect(screen.getByRole('textbox')).not.toBeDisabled()
    })
  })

  describe('Message Display Integration', () => {
    it('displays messages from useChat hook', () => {
      render(<Chat />)
      
      // Should display messages from mock data
      expect(screen.getByText(/social media app for pet owners/i)).toBeInTheDocument()
      expect(screen.getByText(/great idea/i)).toBeInTheDocument()
    })

    it('updates when new messages arrive', () => {
      const { rerender } = render(<Chat />)
      
      // Add a new message
      const newMessages = [
        ...mockDefaultState.messages,
        {
          id: '4',
          role: 'user' as const,
          content: 'New message',
        },
      ]
      
      mockUseStreamingChat.mockReturnValue({
        ...mockDefaultState,
        messages: newMessages,
      } as any)
      
      rerender(<Chat />)
      
      expect(screen.getByText('New message')).toBeInTheDocument()
    })
  })

  describe('Responsive Design Integration', () => {
    it('applies responsive classes correctly', () => {
      render(<Chat />)
      
      const mainContainer = screen.getByText('PRDGeneral').closest('div')?.parentElement
      expect(mainContainer).toHaveClass('max-w-4xl', 'mx-auto')
    })

    it('handles mobile layout', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<Chat />)
      
      // Should still render all components
      expect(screen.getByText('PRDGeneral')).toBeInTheDocument()
      expect(screen.getByRole('textbox')).toBeInTheDocument()
    })
  })

  describe('Accessibility Integration', () => {
    it('maintains proper focus management', async () => {
      const user = userEvent.setup()
      render(<Chat />)
      
      const textarea = screen.getByRole('textbox')
      const submitButton = screen.getByRole('button', { name: /send message/i })
      
      // Tab navigation should work
      await user.tab()
      expect(textarea).toHaveFocus()
      
      await user.tab()
      expect(submitButton).toHaveFocus()
    })

    it('has proper ARIA landmarks', () => {
      render(<Chat />)
      
      expect(screen.getByRole('log')).toBeInTheDocument() // Message list
      expect(screen.getByRole('form')).toBeInTheDocument() // Chat input form
    })

    it('provides screen reader announcements', () => {
      render(<Chat />)
      
      const messageList = screen.getByRole('log')
      expect(messageList).toHaveAttribute('aria-live', 'polite')
    })
  })

  describe('Performance Integration', () => {
    it('renders efficiently with many messages', () => {
      const manyMessages = Array.from({ length: 50 }, (_, i) => ({
        id: `msg-${i}`,
        role: i % 2 === 0 ? 'user' as const : 'assistant' as const,
        content: `Message ${i}`,
      }))
      
      mockUseStreamingChat.mockReturnValue({
        ...mockDefaultState,
        messages: manyMessages,
      } as any)
      
      const startTime = performance.now()
      render(<Chat />)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(100)
      expect(screen.getByText('PRDGeneral')).toBeInTheDocument()
    })
  })

  describe('State Management Integration', () => {
    it('maintains state consistency across components', () => {
      const customState = {
        ...mockDefaultState,
        input: 'Custom input',
        isLoading: true,
      }
      
      mockUseStreamingChat.mockReturnValue(customState as any)
      
      render(<Chat />)
      
      // Input component should reflect the state
      expect(screen.getByRole('textbox')).toHaveValue('Custom input')
      expect(screen.getByRole('textbox')).toBeDisabled()
      
      // Should show sending message button when loading
      expect(screen.getByRole('button', { name: /sending message/i })).toBeInTheDocument()
    })

    it('handles state updates correctly', () => {
      const { rerender } = render(<Chat />)
      
      // Update input state
      mockUseStreamingChat.mockReturnValue({
        ...mockDefaultState,
        input: 'Updated input',
      } as any)
      
      rerender(<Chat />)
      
      expect(screen.getByRole('textbox')).toHaveValue('Updated input')
    })
  })
})
