/**
 * PRD Intent Template & Keyword Library
 *
 * Structured prompt-engineering template for detecting PRD generation intent
 * with confidence scoring and rule-based matching.
 */

export interface KeywordPattern {
  pattern: string | RegExp;
  confidence: number; // 0-1 confidence score
  context?: string[]; // Optional context words that boost confidence
  rule: string; // Rule name for debugging/analytics
}

export interface IntentResult {
  intent: 'clarification' | 'generation';
  rule: string;
  confidence: number;
  matchedPattern?: string;
}

export interface PRDIntentTemplate {
  clarificationMode: {
    keywords: {
      primary: string[];
      secondary: string[];
    };
    patterns: KeywordPattern[];
    confidenceThreshold: number;
  };
  generationMode: {
    keywords: {
      primary: string[];
      secondary: string[];
    };
    patterns: KeywordPattern[];
    confidenceThreshold: number;
    contextBoosters: {
      clarificationComplete: string[];
      prdTerminology: string[];
      completionSignals: string[];
    };
  };
}

/**
 * PRD Intent Template Configuration
 * Based on research from PromptEngineeringforPRDIntent.md and existing codebase patterns
 */
export const prdIntentTemplate: PRDIntentTemplate = {
  clarificationMode: {
    keywords: {
      primary: [
        "what", "how", "why", "when", "where", "who",
        "tell me", "explain", "describe", "clarify",
        "help me understand", "i need to know"
      ],
      secondary: [
        "question", "confused", "not sure", "unclear",
        "more details", "can you", "could you"
      ]
    },
    patterns: [
      {
        pattern: /\b(what|how|why|when|where|who)\b/i,
        confidence: 0.7,
        rule: "question_word"
      },
      {
        pattern: /\b(tell me|explain|describe|clarify)\b/i,
        confidence: 0.8,
        rule: "clarification_request"
      }
    ],
    confidenceThreshold: 0.6
  },

  generationMode: {
    keywords: {
      primary: [
        // Direct PRD requests (high confidence)
        "generate prd", "create prd", "write prd", "build prd", "make prd", "draft prd",
        "generate the prd", "create the prd", "write the prd", "build the prd", "make the prd", "draft the prd",
        "generate my prd", "create my prd", "write my prd", "build my prd", "make my prd",

        // Action-oriented
        "let's generate", "ready to generate", "time to generate", "now generate", "please generate",
        "ready for prd", "ready for the prd", "i'm ready", "we're ready", "let's create", "let's build"
      ],
      secondary: [
        // Natural completion language
        "generate it", "create it", "write it up", "put it together", "make the document",
        "create the document", "write the document",

        // Contextual triggers (require PRD context)
        "let's do this", "let's start", "i'm done", "that's everything", "sounds good",
        "perfect", "exactly", "that's clear", "yes that's right",

        // Completion + action
        "now what", "next step", "what's next", "ready to proceed", "let's move forward"
      ]
    },

    patterns: [
      // Explicit PRD requests (highest confidence)
      {
        pattern: /\b(generate|create|write|build|make|draft)\s+(the\s+|my\s+)?prd\b/i,
        confidence: 0.95,
        rule: "explicit_prd_request"
      },
      {
        pattern: /\bprd\s+(generation|creation|writing|building)\b/i,
        confidence: 0.9,
        rule: "prd_action_noun"
      },

      // Natural language indicators (medium-high confidence)
      {
        pattern: /\b(i'm|we're)\s+ready\s+(for|to)\b/i,
        confidence: 0.8,
        context: ['generate', 'create', 'prd'],
        rule: "readiness_indicator"
      },
      {
        pattern: /\b(let's\s+)?(get\s+)?started\s+(with|on)\b/i,
        confidence: 0.75,
        context: ['prd', 'generation', 'document'],
        rule: "start_action"
      },
      {
        pattern: /\bnow\s+(generate|create|write|build)\b/i,
        confidence: 0.8,
        rule: "immediate_action"
      },

      // Completion indicators (context-dependent)
      {
        pattern: /\b(that's\s+)?(exactly|perfect|correct|right)\b/i,
        confidence: 0.7,
        rule: "completion_confirmation"
      },
      {
        pattern: /\b(yes,?\s+)?(that's\s+it|that\s+works)\b/i,
        confidence: 0.75,
        rule: "agreement_completion"
      },
      {
        pattern: /\bsounds\s+(good|great|perfect)\b/i,
        confidence: 0.7,
        rule: "approval_signal"
      }
    ],

    confidenceThreshold: 0.8,

    contextBoosters: {
      clarificationComplete: [
        "that's clear", "yes that's right", "exactly", "perfect", "that's correct",
        "sounds good", "that works", "i agree", "that's it", "that's the one",
        "makes sense", "understood", "clear", "got it", "that's right"
      ],
      prdTerminology: [
        "requirements", "specification", "document", "feature", "user story",
        "acceptance criteria", "success metrics", "target user", "problem statement",
        "solution", "functionality", "business objective"
      ],
      completionSignals: [
        "all set", "we're good", "that covers it", "nothing else", "that's complete",
        "ready to move on", "i'm done", "that's everything", "no more questions"
      ]
    }
  }
};

/**
 * Negative patterns that should NOT trigger PRD generation
 */
export const negativePatterns: KeywordPattern[] = [
  {
    pattern: /\bwhat is a prd\b/i,
    confidence: 0.95,
    rule: "prd_question"
  },
  {
    pattern: /\bhow to write a prd\b/i,
    confidence: 0.9,
    rule: "prd_instruction_request"
  },
  {
    pattern: /\bprd (template|example)\b/i,
    confidence: 0.85,
    rule: "prd_resource_request"
  },
  {
    pattern: /\b(if|maybe|perhaps)\s+(we\s+)?(generate|create)\b/i,
    confidence: 0.75,
    rule: "hypothetical_scenario"
  },
  {
    pattern: /\bshould\s+we\s+(generate|create|write|build)\b/i,
    confidence: 0.85,
    rule: "question_about_generation"
  },
  {
    pattern: /\b(generated|created|wrote)\s+a\s+prd\b/i,
    confidence: 0.75,
    rule: "past_tense_reference"
  },
  {
    pattern: /\b(before|after|once|when)\s+(we\s+)?(generate|create|write|build)\b/i,
    confidence: 0.7,
    rule: "conditional_statement"
  },
  {
    pattern: /\b(after|once)\s+(we\s+)?(generate|create|write|build)\s+(prd|the\s+prd)\b/i,
    confidence: 0.8,
    rule: "conditional_with_prd"
  }
];

/**
 * Helper function to check if a keyword matches with word boundaries
 * Handles both single words and multi-word phrases correctly
 */
function matchesWithWordBoundary(text: string, keyword: string): boolean {
  const normalizedKeyword = keyword.toLowerCase();

  // For multi-word phrases, we need to be more careful with word boundaries
  if (normalizedKeyword.includes(' ')) {
    // Split the keyword into words and create a pattern that allows for word boundaries
    const words = normalizedKeyword.split(/\s+/);
    const escapedWords = words.map(word => word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'));
    const pattern = `\\b${escapedWords.join('\\s+')}\\b`;
    const regex = new RegExp(pattern, 'i');
    return regex.test(text);
  } else {
    // Single word - use simple word boundary
    const escapedKeyword = normalizedKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`\\b${escapedKeyword}\\b`, 'i');
    return regex.test(text);
  }
}

/**
 * Core intent matching utility function
 * Analyzes text input and returns intent classification with confidence score
 */
export function matchIntent(text: string): IntentResult {
  const normalizedText = text.toLowerCase().trim();

  // Check negative patterns first (early exit)
  for (const negPattern of negativePatterns) {
    if (typeof negPattern.pattern === 'string') {
      if (matchesWithWordBoundary(normalizedText, negPattern.pattern)) {
        return {
          intent: 'clarification',
          rule: `negative_${negPattern.rule}`,
          confidence: negPattern.confidence,
          matchedPattern: negPattern.pattern.toString()
        };
      }
    } else if (negPattern.pattern.test(normalizedText)) {
      return {
        intent: 'clarification',
        rule: `negative_${negPattern.rule}`,
        confidence: negPattern.confidence,
        matchedPattern: negPattern.pattern.toString()
      };
    }
  }

  // Check primary keywords (high confidence)
  for (const keyword of prdIntentTemplate.generationMode.keywords.primary) {
    if (matchesWithWordBoundary(normalizedText, keyword)) {
      return {
        intent: 'generation',
        rule: 'primary_keyword_match',
        confidence: 0.9,
        matchedPattern: keyword
      };
    }
  }

  // Check secondary keywords (medium confidence)
  for (const keyword of prdIntentTemplate.generationMode.keywords.secondary) {
    if (matchesWithWordBoundary(normalizedText, keyword)) {
      return {
        intent: 'generation',
        rule: 'secondary_keyword_match',
        confidence: 0.75,
        matchedPattern: keyword
      };
    }
  }

  // Check generation mode patterns (for complex RegExp patterns)
  for (const pattern of prdIntentTemplate.generationMode.patterns) {
    let isMatch = false;
    let matchedText = '';

    if (typeof pattern.pattern === 'string') {
      isMatch = matchesWithWordBoundary(normalizedText, pattern.pattern);
      matchedText = pattern.pattern;
    } else {
      const match = pattern.pattern.exec(normalizedText);
      isMatch = !!match;
      matchedText = match ? match[0] : pattern.pattern.toString();
    }

    if (isMatch) {
      let confidence = pattern.confidence;

      // Apply context boosters if specified
      if (pattern.context) {
        const contextMatches = pattern.context.filter(ctx =>
          normalizedText.includes(ctx.toLowerCase())
        ).length;

        if (contextMatches > 0) {
          confidence = Math.min(confidence + (contextMatches * 0.05), 0.98);
        } else {
          // Reduce confidence if context is required but not found
          confidence = Math.max(confidence - 0.2, 0.3);
        }
      }

      if (confidence >= prdIntentTemplate.generationMode.confidenceThreshold) {
        return {
          intent: 'generation',
          rule: pattern.rule,
          confidence,
          matchedPattern: matchedText
        };
      }
    }
  }

  // Default to clarification mode
  return {
    intent: 'clarification',
    rule: 'default_clarification',
    confidence: 0.1
  };
}

/**
 * Enhanced intent matching with conversation context
 * Considers conversation history and completion indicators
 */
export function matchIntentWithContext(
  text: string,
  conversationHistory?: string[],
  messageCount?: number
): IntentResult {
  const baseResult = matchIntent(text);

  // If already high confidence generation intent, return as-is
  if (baseResult.intent === 'generation' && baseResult.confidence >= 0.9) {
    return baseResult;
  }

  // Apply conversation context boosters
  if (conversationHistory && messageCount) {
    const fullContext = conversationHistory.join(' ').toLowerCase();
    let confidenceBoost = 0;

    // Check for clarification completion indicators
    const completionMatches = prdIntentTemplate.generationMode.contextBoosters.clarificationComplete
      .filter(indicator => fullContext.includes(indicator.toLowerCase())).length;

    if (completionMatches >= 2) {
      confidenceBoost += 0.1;
    }

    // Check for PRD terminology density
    const terminologyMatches = prdIntentTemplate.generationMode.contextBoosters.prdTerminology
      .filter(term => fullContext.includes(term.toLowerCase())).length;

    if (terminologyMatches >= 4) {
      confidenceBoost += 0.05;
    }

    // Conversation length factor
    if (messageCount >= 8) {
      confidenceBoost += 0.05;
    }

    // Apply boost and re-evaluate intent
    if (confidenceBoost > 0) {
      const boostedConfidence = Math.min(baseResult.confidence + confidenceBoost, 0.98);

      if (boostedConfidence >= prdIntentTemplate.generationMode.confidenceThreshold) {
        return {
          ...baseResult,
          intent: 'generation',
          confidence: boostedConfidence,
          rule: `${baseResult.rule}_context_boosted`
        };
      }
    }
  }

  return baseResult;
}
