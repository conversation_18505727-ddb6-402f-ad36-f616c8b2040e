/**
 * Shared session utilities for both frontend and backend
 * 
 * This module provides session ID generation that works across
 * different environments (browser, Node.js, etc.)
 */

/**
 * Generate cryptographically secure session ID
 * 
 * This function provides multiple fallback mechanisms to ensure
 * a secure session ID is generated across different environments:
 * 1. crypto.randomUUID() - Modern browsers and Node.js 16+
 * 2. crypto.getRandomValues() - Browsers
 * 3. Node.js crypto.randomBytes() - Node.js environments
 * 4. Fallback using timestamp + random (not cryptographically secure)
 */
export function generateSecureSessionId(): string {
  // Use crypto.randomUUID if available (preferred method)
  if (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
    return crypto.randomUUID();
  }

  // Use crypto.getRandomValues in browsers
  if (typeof crypto !== 'undefined' && typeof crypto.getRandomValues === 'function') {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return 'sess_' + Array.from(array).map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // Use Node.js crypto.randomBytes if available
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const nodeCrypto = require('crypto');
    if (typeof nodeCrypto.randomBytes === 'function') {
      return 'sess_' + nodeCrypto.randomBytes(16).toString('hex');
    }
  } catch {
    // Ignore if require fails (e.g., in browser environments)
  }

  // Final fallback (not cryptographically secure, but should rarely be used)
  console.warn('Using non-cryptographic fallback for session ID generation');
  return 'sess_fallback_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2);
}

/**
 * Validate session ID format
 * 
 * Checks if a session ID matches expected formats:
 * - UUID v4 format (xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx)
 * - sess_ prefixed hex string
 * - sess_fallback_ prefixed string (fallback format)
 * 
 * @param sessionId - The session ID to validate
 * @returns boolean - True if the format is valid
 */
export function isValidSessionIdFormat(sessionId: string): boolean {
  if (!sessionId || typeof sessionId !== 'string') {
    return false;
  }

  // UUID v4 format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(sessionId)) {
    return true;
  }

  // sess_ prefixed hex format
  const sessHexRegex = /^sess_[0-9a-f]{32}$/i;
  if (sessHexRegex.test(sessionId)) {
    return true;
  }

  // sess_fallback_ format
  // sess_fallback_ format - matches the actual generation pattern
  const sessFallbackRegex = /^sess_fallback_[0-9a-z]{1,13}_[0-9a-z]{11,}$/;
  if (sessFallbackRegex.test(sessionId)) {
    return true;
  }

  return false;
}
