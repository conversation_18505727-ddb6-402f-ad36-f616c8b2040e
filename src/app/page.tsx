"use client";

import Link from 'next/link';
import { AuthButton } from '@/components/AuthButton';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Authentication Section */}
        <div className="flex justify-end mb-8">
          <AuthButton />
        </div>
        
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            PRDGeneral
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Transform your product ideas into crystal-clear PRDs through AI-powered clarification
          </p>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              How it works
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 dark:text-blue-300 font-bold">1</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Problem Definition</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Define the specific problem you&apos;re solving</p>
              </div>
              <div className="text-center">
                <div className="bg-green-100 dark:bg-green-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 dark:text-green-300 font-bold">2</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Core Features</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Identify the ONE core feature that matters</p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 dark:bg-purple-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 dark:text-purple-300 font-bold">3</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Target Users</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Clarify exactly who will use your product</p>
              </div>
              <div className="text-center">
                <div className="bg-orange-100 dark:bg-orange-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-orange-600 dark:text-orange-300 font-bold">4</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Success Metrics</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Define measurable success criteria</p>
              </div>
            </div>
          </div>
          <Link
            href="/chat"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
          >
            Start Clarifying Your Idea
          </Link>
        </div>
      </div>
    </div>
  );
}
