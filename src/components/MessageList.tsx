import { useEffect, useRef, useMemo, useCallback } from 'react';
import Message from './Message';
import { Message as MessageType } from '../types';

// Debounce utility function with faster streaming updates
function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface MessageListProps {
  messages: MessageType[];
  isLoading: boolean;
  isStreaming?: boolean;
  currentStreamingMessage?: MessageType | null;
}

export default function MessageList({ 
  messages, 
  isLoading, 
  isStreaming = false, 
  currentStreamingMessage 
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Faster debounce for streaming (16ms ≈ 60fps) for smooth streaming experience
  const debouncedScrollToBottom = useMemo(
    () => debounce(scrollToBottom, isStreaming ? 16 : 100),
    [scrollToBottom, isStreaming]
  );

  useEffect(() => {
    debouncedScrollToBottom();
  }, [messages, isLoading, currentStreamingMessage, debouncedScrollToBottom]);

  return (
    <div
      className="flex-1 overflow-y-auto space-y-2 sm:space-y-3 lg:space-y-4 px-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600"
      role="log"
      aria-label="Chat messages"
      aria-live="polite"
      aria-relevant="additions"
    >
      {(!messages || messages.length === 0) && (
        <div className="text-center text-gray-500 dark:text-gray-400 mt-4 sm:mt-6 lg:mt-8 px-2 sm:px-4">
          <p className="text-base sm:text-lg lg:text-xl mb-3 sm:mb-4">👋 Welcome to PRDGeneral!</p>
          <p className="mb-3 sm:mb-4 text-xs sm:text-sm lg:text-base">I&apos;m here to help you clarify your product ideas through a rigorous 4-step process:</p>
          <ol className="text-left max-w-sm sm:max-w-md mx-auto space-y-1 sm:space-y-2 text-xs sm:text-sm lg:text-base">
            <li><strong>1.</strong> Specific Problem Definition</li>
            <li><strong>2.</strong> Core Feature Identification</li>
            <li><strong>3.</strong> Target User Clarity</li>
            <li><strong>4.</strong> Success Metrics Definition</li>
          </ol>
          <p className="mt-4 sm:mt-6 text-xs sm:text-sm lg:text-base">Tell me about your product idea to get started!</p>
        </div>
      )}
      
      {messages && messages.map((message) => (
        <Message
          key={message.id}
          message={message}
        />
      ))}
      
      {isLoading && !isStreaming && (
        <div className="flex justify-start" role="status" aria-label="AI is generating response">
          <div className="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white max-w-[85%] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 sm:py-3 rounded-lg rounded-bl-sm">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1" aria-hidden="true">
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-sm">PRDGeneral is thinking...</span>
            </div>
          </div>
        </div>
      )}

      {isStreaming && (
        <div className="flex justify-start" role="status" aria-label="AI is streaming response" aria-live="polite">
          <div className="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white max-w-[85%] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 sm:py-3 rounded-lg rounded-bl-sm">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-600 dark:text-blue-400">Streaming...</span>
            </div>
            {/* Show streaming content with cursor */}
            <div className="relative">
              <span className="whitespace-pre-wrap">{currentStreamingMessage?.content || ''}</span>
              <span className="inline-block w-0.5 h-4 bg-gray-500 ml-1 animate-pulse"></span>
            </div>
          </div>
        </div>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}
