import { FormEvent, KeyboardEvent, useRef, useEffect, useState, useCallback } from 'react';

const MAX_INPUT_HEIGHT = 120; // pixels
const MAX_MESSAGE_LENGTH = 10000; // characters - matches server validation

interface ChatInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  messageCount?: number; // For validation against server limits
  isStreaming?: boolean;
  canCancel?: boolean;
  onCancel?: () => void;
}

export default function ChatInput({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  messageCount = 0,
  isStreaming = false,
  canCancel = false,
  onCancel
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [validationError, setValidationError] = useState<string>('');

  // Client-side validation function
  const validateInput = useCallback((value: string): string => {
    // Remove null bytes (matches server sanitization)
    const sanitizedValue = value.replace(/\0/g, '');

    // Check message length
    if (sanitizedValue.length > MAX_MESSAGE_LENGTH) {
      return `Message too long. Maximum ${MAX_MESSAGE_LENGTH.toLocaleString()} characters allowed.`;
    }

    // Check message count (server allows max 50)
    if (messageCount >= 50) {
      return 'Too many messages. Maximum 50 messages allowed per conversation.';
    }

    return '';
  }, [messageCount]);

  // Auto-resize textarea and validate input
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, MAX_INPUT_HEIGHT)}px`;
    }

    // Validate input and set error state
    const error = validateInput(input);
    setValidationError(error);
  }, [input, messageCount, validateInput]);

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading && input.trim() && !validationError) {
        const form = e.currentTarget.form;
        if (form) {
          form.requestSubmit();
        }
      }
    }
  };

  // Enhanced submit handler with validation
  const handleValidatedSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Final validation before submit
    const error = validateInput(input);
    if (error) {
      setValidationError(error);
      return;
    }

    // Clear any previous errors and proceed with submit
    setValidationError('');
    handleSubmit(e);
  };

  const isSubmitDisabled = isLoading || !input.trim() || !!validationError;
  const charactersRemaining = MAX_MESSAGE_LENGTH - input.length;
  const isNearLimit = charactersRemaining < 1000;

  return (
    <div className="flex-shrink-0">
      <form onSubmit={handleValidatedSubmit} className="flex gap-2 sm:gap-3 items-end" role="form" aria-label="Chat message form">
        <div className="flex-1">
          <label htmlFor="chat-input" className="sr-only">
            Enter your product idea or question
          </label>
          <textarea
            id="chat-input"
            data-testid="message-input"
            ref={textareaRef}
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Describe your product idea... (Press Enter to send, Shift+Enter for new line)"
            className={`w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors resize-none min-h-[44px] max-h-[${MAX_INPUT_HEIGHT}px] ${
              validationError
                ? 'border-red-500 focus:ring-red-500'
                : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
            }`}
            disabled={isLoading}
            rows={1}
            aria-label="Chat message input"
            aria-describedby="chat-input-help"
          />
          <div id="chat-input-help" className="sr-only">
            Press Enter to send your message, or Shift+Enter to add a new line
          </div>

          {/* Character count and validation messages */}
          <div className="flex justify-between items-center mt-1 text-xs">
            <div>
              {validationError && (
                <span className="text-red-500" role="alert" aria-live="polite">
                  {validationError}
                </span>
              )}
            </div>
            <div className={`${isNearLimit ? 'text-orange-500' : 'text-gray-500 dark:text-gray-400'}`}>
              {charactersRemaining.toLocaleString()} characters remaining
            </div>
          </div>
        </div>
        {isStreaming && canCancel && onCancel ? (
          <button
            type="button"
            onClick={onCancel}
            className="px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors font-medium min-h-[44px]"
            aria-label="Stop generation"
          >
            Stop
          </button>
        ) : (
          <button
            type="submit"
            data-testid="submit-button"
            disabled={isSubmitDisabled}
            className="px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium min-h-[44px]"
            aria-label={isLoading ? "Sending message..." : "Send message"}
            aria-disabled={isSubmitDisabled}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"
                  aria-hidden="true"
                  role="status"
                ></div>
                <span className="hidden sm:inline">{isStreaming ? 'Streaming...' : 'Sending...'}</span>
              </div>
            ) : (
              'Send'
            )}
          </button>
        )}
      </form>
    </div>
  );
}
