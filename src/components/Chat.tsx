'use client';

import MessageList from './MessageList';
import ChatInput from './ChatInput';
import ErrorBoundary from './ErrorBoundary';
import { useStreamingChat } from '../hooks/useStreamingChat';

export default function Chat() {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    isStreaming,
    isError,
    canCancel,
    cancelStream,
    error,
    currentStreamingMessage
  } = useStreamingChat();

  return (
    <div className="flex flex-col h-screen max-w-4xl mx-auto p-3 sm:p-4 lg:p-6">
      {/* Header */}
      <div className="flex-shrink-0 mb-3 sm:mb-4 lg:mb-6 text-center">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-1 sm:mb-2">
          PRDGeneral
        </h1>
        <p className="text-xs sm:text-sm lg:text-base text-gray-600 dark:text-gray-400">
          Transform your product ideas into crystal-clear PRDs
        </p>

        {/* Streaming Status Indicator */}
        {isStreaming && (
          <div className="mt-2 flex items-center justify-center gap-2 text-sm text-blue-600" data-testid="streaming-indicator">
            <div className="animate-pulse">●</div>
            <span>AI is responding...</span>
            {canCancel && (
              <button
                onClick={cancelStream}
                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
              >
                Stop
              </button>
            )}
          </div>
        )}

        {/* Error Display */}
        {isError && error && (
          <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded" data-testid="error-display">
            Error: {error}
          </div>
        )}
      </div>

      {/* Messages Container */}
      <div className="flex-1 min-h-0 mb-4 flex flex-col">
        <ErrorBoundary>
          <MessageList
            messages={messages}
            isLoading={isLoading}
            isStreaming={isStreaming}
            currentStreamingMessage={currentStreamingMessage}
          />
        </ErrorBoundary>
      </div>

      {/* Input Form */}
      <ErrorBoundary>
        <ChatInput
          input={input}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          isLoading={isLoading}
          messageCount={messages.length}
          isStreaming={isStreaming}
          canCancel={canCancel}
          onCancel={cancelStream}
        />
      </ErrorBoundary>
    </div>
  );
}
