/**
 * Shared message types for the chat system
 * Re-export AI SDK Message type for compatibility
 */

export type { Message } from 'ai';

// Streaming chunk types for real-time chat
export type StreamingChunk =
  | { type: 'status'; status: 'started' | 'cancelled'; messageId: string; timestamp: number }
  | { type: 'chunk'; content: string; messageId: string; timestamp: number }
  | {
      type: 'complete';
      content: string;
      provider: string;
      model: string;
      generationTime: number;
      messageId: string;
      timestamp: number
    }
  | { type: 'error'; error: string; messageId: string; timestamp: number };
