'use client';

import { useState, useCallback, useRef } from 'react';
import { useAction, useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Message } from '../types';

export interface StreamingChatState {
  messages: Message[];
  input: string;
  isLoading: boolean;
  isStreaming: boolean;
  isError: boolean;
  canCancel: boolean;
  error: string | null;
  currentStreamingMessage: Message | null;
  threadId: string | undefined;
}

export interface StreamingChatActions {
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  cancelStream: () => Promise<void>;
  retry: () => Promise<void>;
}

export function useStreamingChat(initialThreadId?: string) {
  // Local state for thread management
  const [threadId, setThreadId] = useState<string | undefined>(initialThreadId);

  // Convex hooks using the new agent approach
  const streamChatResponse = useAction(api.agent.streamChatResponse);
  const createThread = useAction(api.agent.createThread);
  const currentUser = useQuery(api.users.current);
  const storedMessages = useQuery(api.messages.getThreadMessages,
    threadId ? { threadId } : 'skip'
  );

  // Local state for real-time streaming
  const [input, setInput] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<Message | null>(null);

  // References for streaming control
  const streamingAbortController = useRef<AbortController | null>(null);

  // Use stored messages directly (agent handles streaming internally)
  // Only add currentStreamingMessage if it's an assistant message (for streaming display)
  const messages: Message[] = [
    ...(storedMessages || []).map(msg => {
      // Safely convert createdAt to Date with validation
      let createdAt: Date;

      if (msg.createdAt) {
        const parsedDate = new Date(msg.createdAt);
        // Check if the date is valid (not NaN)
        createdAt = isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
      } else {
        // Fallback to current date if createdAt is missing
        createdAt = new Date();
      }

      return {
        ...msg,
        createdAt
      };
    }),
    ...(currentStreamingMessage && currentStreamingMessage.role === 'assistant' ? [currentStreamingMessage] : [])
  ];

  const isLoading = isStreaming && !currentStreamingMessage;
  const canCancel = isStreaming && streamingAbortController.current !== null;

  // Handle input changes
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInput(e.target.value);
  }, []);

  // Cancel current stream
  const cancelStream = useCallback(async () => {
    if (streamingAbortController.current) {
      streamingAbortController.current.abort();
      setIsStreaming(false);
      setCurrentStreamingMessage(null);
      streamingAbortController.current = null;
    }
  }, []);

  // Submit message using agent approach
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim() || isStreaming) return;

    const userMessage = input.trim();
    setInput('');
    setIsStreaming(true);
    setIsError(false);
    setError(null);

    try {
      // Ensure we have a thread
      let activeThreadId = threadId;
      if (!activeThreadId) {
        try {
          activeThreadId = await createThread({
            title: userMessage.substring(0, 50) + (userMessage.length > 50 ? '...' : ''),
          });
          setThreadId(activeThreadId);
        } catch {
          setIsError(true);
          setError('Failed to create chat thread');
          setIsStreaming(false);
          return;
        }
      }

      // Create abort controller for cancellation
      const abortController = new AbortController();
      streamingAbortController.current = abortController;

      // Don't show optimistic user message - let the agent system handle it
      // This prevents duplication issues

      // For now, use the simple approach while streaming is being fixed
      // TODO: Implement proper streaming with the agent
      await streamChatResponse({
        threadId: activeThreadId,
        prompt: userMessage,
        userId: currentUser?._id || "anonymous", // Use authenticated user ID or fallback to anonymous
      });

      // Clear any streaming message state
      setCurrentStreamingMessage(null);

    } catch (err) {
      console.error('Submit error:', err);
      setIsError(true);
      setError(err instanceof Error ? err.message : 'Failed to send message');
      setCurrentStreamingMessage(null);
    } finally {
      setIsStreaming(false);
      streamingAbortController.current = null;
    }
  }, [input, isStreaming, threadId, streamChatResponse, createThread, currentUser?._id]);

  // Retry last message
  const retry = useCallback(async () => {
    if (isStreaming || !storedMessages?.length) return;

    const lastMessage = storedMessages[storedMessages.length - 1];
    if (lastMessage.role !== 'user') return;

    setInput(lastMessage.content);
    // The user can then submit again
  }, [isStreaming, storedMessages]);

  const state: StreamingChatState = {
    messages,
    input,
    isLoading,
    isStreaming,
    isError,
    canCancel,
    error,
    currentStreamingMessage,
    threadId,
  };

  const actions: StreamingChatActions = {
    handleInputChange,
    handleSubmit,
    cancelStream,
    retry,
  };

  return { ...state, ...actions };
}