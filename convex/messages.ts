import { query } from "./_generated/server";
import { v } from "convex/values";
import { listMessages, type MessageDoc } from "@convex-dev/agent";
import { components } from "./_generated/api";
import { paginationOptsValidator } from "convex/server";

// Query to retrieve complete messages only (no streaming deltas)
export const listThreadMessages = query({
  args: {
    threadId: v.string(),
    paginationOpts: paginationOptsValidator,
  },
  handler: async (ctx, { threadId, paginationOpts }) => {
    // This will only return complete messages since we're not saving deltas
    const messages = await listMessages(ctx, components.agent, {
      threadId,
      paginationOpts,
    });

    return messages;
  },
});

// Get a single thread's messages without pagination
export const getThreadMessages = query({
  args: {
    threadId: v.string(),
  },
  handler: async (ctx, { threadId }) => {
    const messages = await listMessages(ctx, components.agent, {
      threadId,
      paginationOpts: { numItems: 100, cursor: null },
    });

    // Transform to match AI SDK Message format and reverse order (newest first)
    return messages.page
      .filter((msg: MessageDoc) => {
        // Filter out tool messages since AI SDK doesn't support them
        const role = msg.message?.role;
        return role === 'user' || role === 'assistant' || role === 'system';
      })
      .map((msg: MessageDoc) => ({
        id: msg._id,
        role: msg.message!.role as 'user' | 'assistant' | 'system',
        content: convertContentToString(msg.message!.content),
        createdAt: msg._creationTime, // Return timestamp as number, not Date object
      }))
      .reverse(); // Reverse to show newest messages first
  },
});

// Type definitions for AI SDK message content
type TextContent = { text: string; type?: string };
type ImageContent = { type: "image"; image: string | ArrayBuffer; mimeType?: string };
type MessageContentPart = string | TextContent | ImageContent | { type: string; [key: string]: unknown };
type MessageContent = string | MessageContentPart[];

/**
 * Converts message content of various types into a single string.
 *
 * Handles content that may be a string, an array of strings or objects with text fields, or other types. Non-text elements are replaced with a placeholder.
 *
 * @param content - The message content to convert.
 * @returns The content represented as a string.
 */
function convertContentToString(content: MessageContent): string {
  if (typeof content === 'string') {
    return content;
  }

  if (Array.isArray(content)) {
    return content
      .map(part => {
        if (typeof part === 'string') return part;
        if (part && typeof part === 'object' && 'text' in part) {
          const textPart = part as TextContent;
          return textPart.text;
        }
        if (part && typeof part === 'object' && 'type' in part) {
          const typedPart = part as { type: string; [key: string]: unknown };
          if (typedPart.type === 'text' && 'text' in typedPart) {
            return (typedPart as TextContent).text;
          }
        }
        return '[non-text content]';
      })
      .join(' ');
  }

  return String(content || '');
}