/// <reference types="vite/client" />
import { convexTest } from "convex-test";
import { expect, test } from "vitest";
import { api } from "./_generated/api";
import schema from "./schema";

// Import all Convex function modules for testing
const modules = import.meta.glob("./**/!(*.*.*)*.*s");

test("can create conversation", async () => {
  const t = convexTest(schema, modules);

  const conversationId = await t.mutation(api.chat.createConversation, {
    sessionId: "test-session",
  });

  expect(conversationId).toBeDefined();
  expect(typeof conversationId).toBe("string");
});

test("can get conversation", async () => {
  const t = convexTest(schema, modules);

  const conversationId = await t.mutation(api.chat.createConversation, {
    sessionId: "test-session-get",
  });

  const conversation = await t.query(api.chat.getConversation, {
    conversationId,
  });

  expect(conversation).toBeDefined();
  expect(conversation!.sessionId).toBe("test-session-get");
});
