import { Agent } from "@convex-dev/agent";
import { components } from "./_generated/api";
import { v } from "convex/values";
import { action } from "./_generated/server";
import { openai } from "@ai-sdk/openai";

// Initialize the agent with gpt-4o-mini for cost efficiency
const chatAgent = new Agent(components.agent, {
  chat: openai("gpt-4o-mini"),
  instructions: `You are a helpful assistant for PRDGeneral, a tool that helps transform product ideas into crystal-clear PRDs through a rigorous 4-step process:
1. Specific Problem Definition
2. Core Feature Identification
3. Target User Clarity
4. Success Metrics Definition

Be concise, friendly, and focus on guiding users through this process.`,
});

// Create a new thread using the agent's thread management
export const createThread = action({
  args: {
    userId: v.optional(v.string()),
    title: v.optional(v.string()),
  },
  handler: async (ctx, { userId, title }) => {
    // Create a new thread using the agent
    const { thread } = await chatAgent.createThread(ctx, {
      userId: userId || "anonymous",
      title: title || "New Conversation"
    });

    return thread.threadId;
  },
});

// PATTERN: Stream to client, save complete message only
export const streamChatResponse = action({
  args: {
    threadId: v.string(),
    prompt: v.string(),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, { threadId, prompt }) => {
    try {
      // Continue thread with streaming - agent handles message saving internally
      const { thread } = await chatAgent.continueThread(ctx, { threadId });

      // CRITICAL: Set saveStreamDeltas to false to avoid saving partial messages
      const result = await thread.streamText(
        { prompt },
        {
          saveStreamDeltas: false // Only save final complete message
        }
      );

      // Consume stream to ensure completion and final save
      await result.consumeStream();

      return { success: true, threadId };
    } catch (error) {
      console.error('Error in streamChatResponse:', error);

      // Return failure response with error details
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred during streaming',
        threadId
      };
    }
  },
});

