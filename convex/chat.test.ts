/// <reference types="vite/client" />
import { convexTest } from "convex-test";
import { expect, test } from "vitest";
import { api } from "./_generated/api";
import schema from "./schema";

// Import all Convex function modules for testing
const modules = import.meta.glob("./**/!(*.*.*)*.*s");

test("basic conversation flow", async () => {
  const t = convexTest(schema, modules);

  // Test creating a conversation
  const conversationId = await t.mutation(api.chat.createConversation, {
    sessionId: "test-session-123",
  });

  expect(conversationId).toBeDefined();

  // Test adding a message
  const messageId = await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "user",
    content: "Hello, I want to create a PRD for a mobile app",
    messageIndex: 0,
  });

  expect(messageId).toBeDefined();

  // Test getting messages
  const messages = await t.query(api.chat.getMessages, {
    conversationId,
  });

  expect(messages).toHaveLength(1);
  expect(messages[0].content).toBe("Hello, I want to create a PRD for a mobile app");
  expect(messages[0].role).toBe("user");

  // Test getting conversation details
  const conversation = await t.query(api.chat.getConversation, {
    conversationId,
  });

  expect(conversation).toBeDefined();
  expect(conversation!.sessionId).toBe("test-session-123");
  expect(conversation!.status).toBe("active");
  expect(conversation!.currentMode).toBe("clarification");
});

test("session ID uniqueness enforcement", async () => {
  const t = convexTest(schema, modules);

  // Create first conversation with a specific session ID
  const conversationId1 = await t.mutation(api.chat.createConversation, {
    sessionId: "duplicate-test-session",
  });

  // Try to create another conversation with the same session ID
  // According to ensureUniqueSessionId, it should generate a new unique session ID
  const conversationId2 = await t.mutation(api.chat.createConversation, {
    sessionId: "duplicate-test-session",
  });

  expect(conversationId1).not.toBe(conversationId2);

  // Verify both conversations exist but have different session IDs
  const conversation1 = await t.query(api.chat.getConversation, {
    conversationId: conversationId1,
  });
  const conversation2 = await t.query(api.chat.getConversation, {
    conversationId: conversationId2,
  });

  expect(conversation1).toBeDefined();
  expect(conversation2).toBeDefined();

  // Session IDs should be different due to uniqueness enforcement
  expect(conversation1!.sessionId).not.toBe(conversation2!.sessionId);
  expect(conversation1!.sessionId).toBe("duplicate-test-session");
  // Generated session ID can be either UUID format or sess_ format depending on crypto availability
  expect(conversation2!.sessionId).toMatch(/^(sess_|[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/);
});

test("message ordering and indexing", async () => {
  const t = convexTest(schema, modules);

  const conversationId = await t.mutation(api.chat.createConversation, {
    sessionId: "test-ordering-session",
  });

  // Add multiple messages
  await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "user",
    content: "First message",
    messageIndex: 0,
  });

  await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "assistant",
    content: "First response",
    messageIndex: 1,
  });

  await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "user",
    content: "Second message",
    messageIndex: 2,
  });

  const messages = await t.query(api.chat.getMessages, {
    conversationId,
  });

  expect(messages).toHaveLength(3);
  expect(messages[0].content).toBe("First message");
  expect(messages[0].messageIndex).toBe(0);
  expect(messages[1].content).toBe("First response");
  expect(messages[1].messageIndex).toBe(1);
  expect(messages[2].content).toBe("Second message");
  expect(messages[2].messageIndex).toBe(2);
});

test("conversation mode detection and updates", async () => {
  const t = convexTest(schema, modules);

  const conversationId = await t.mutation(api.chat.createConversation, {
    sessionId: "test-mode-session",
  });

  // Initial state should be clarification
  const conversation = await t.query(api.chat.getConversation, {
    conversationId,
  });
  expect(conversation!.currentMode).toBe("clarification");
  expect(conversation!.clarificationComplete).toBe(false);

  // Add some messages to trigger mode detection
  await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "user",
    content: "I want to build a mobile app",
    messageIndex: 0,
  });

  await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "assistant",
    content: "What problem does your app solve?",
    messageIndex: 1,
  });

  await t.mutation(api.chat.addMessage, {
    conversationId,
    role: "user",
    content: "It helps users track their fitness goals",
    messageIndex: 2,
  });

  // Update conversation mode based on messages
  const modeUpdate = await t.mutation(api.chat.updateConversationMode, {
    conversationId,
  });

  expect(modeUpdate.mode).toBe("clarification"); // Still in clarification with limited messages
  expect(modeUpdate.clarificationComplete).toBe(false);
});

test("error handling for invalid inputs", async () => {
  const t = convexTest(schema, modules);

  // Test getting messages for non-existent conversation (using valid ID format)
  await expect(async () => {
    await t.query(api.chat.getMessages, {
      // @ts-expect-error - Testing with non-existent but valid format ID
      conversationId: "k17f8f8f8f8f8f8f8f8f8f8f8f8f8f8f8",
    });
  }).rejects.toThrow(/not found|Resource not found|Conversation not found|Validator error/i);

  // Test adding message to non-existent conversation
  await expect(async () => {
    await t.mutation(api.chat.addMessage, {
      // @ts-expect-error - Testing with non-existent but valid format ID
      conversationId: "k17f8f8f8f8f8f8f8f8f8f8f8f8f8f8f8",
      role: "user",
      content: "Test message",
      messageIndex: 0,
    });
  }).rejects.toThrow(/not found|Resource not found|Conversation not found|Validator error/i);
});

// Legacy streaming tests removed - agent system handles streaming internally