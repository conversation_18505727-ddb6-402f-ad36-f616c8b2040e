/**
 * Shared types for the cascade workflow system
 */

import type { Id } from "../_generated/dataModel";

/**
 * Possible reasons why a gate might fail
 */
export type GateFailureReason =
  | "technical_jargon_without_context"
  | "solution_first_language"
  | "architecture_buzzwords_without_problem"
  | "too_broad_or_generic"
  | "lacks_specific_context"
  | "insufficient_detail"
  | "unclear_requirements"
  | "missing_context"
  | "too_many_features"
  | "missing_problem_statement"
  | "missing_target_user"
  | "missing_core_feature"
  | "missing_success_metrics"
  | "refinement_loop_exhausted";

/**
 * Basic gate result structure
 */
export type GateResult = {
  status: "pass" | "fail";
  message: string;
};

/**
 * Detailed gate result with additional metadata
 */
export type DetailedGateResult = GateResult & {
  failureReasons?: GateFailureReason[];
  confidence?: number;
  processingTime?: number;
  metadata?: Record<string, unknown>;
};

/**
 * Context passed to gate functions
 */
export type GateContext = {
  userInput: string;
  conversationId: Id<"conversations">;
  previousMessages: Array<{
    role: "user" | "assistant" | "system";
    content: string;
    timestamp: number;
  }>;
  priorGateResults: Record<string, DetailedGateResult>;
};

/**
 * Configuration for a gate in the cascade
 */
export type GateConfig = {
  name: string;
  enabled: boolean;
  weight: number;
  timeout?: number;
  retries?: number;
};

/**
 * Overall cascade workflow result
 */
export type CascadeResult = {
  status: "pass" | "fail";
  message: string;
  gateResults: Record<string, DetailedGateResult>;
  totalProcessingTime: number;
  failedAt?: string;
};
