/**
 * Specificity Gate - Fourth and final gate in the cascade flow
 *
 * Runs a refinement loop (max 3 iterations) to ensure concrete details on:
 * - Problem statement
 * - Core feature
 * - Target user
 * - Success metrics
 */

import type { MutationCtx } from "../../_generated/server";
import {
  type DetailedGateResult,
  type GateContext,
  type GateFailureReason
} from "../sharedTypes";

/**
 * Required specificity criteria
 */
const SPECIFICITY_CRITERIA = {
  targetUser: {
    patterns: [
      /\b(?:for|to|targeting|aimed at)\s+(?:busy|early|solo|small|enterprise|startup|freelance|remote|mobile|web|senior|junior|experienced|new|existing)\s+(?:founders?|marketers?|developers?|designers?|managers?|users?|customers?|clients?|professionals?|teams?|companies?|businesses?)\b/gi,
      /\b(?:target audience|target users?|user base|customer base|ideal customer|persona)\b/gi
    ],
    reason: "missing_target_user" as GateFailureReason,
    weight: 0.8
  },

  coreFeature: {
    patterns: [
      /\b(?:the core feature is|main feature|primary function|key capability|central function)\b/gi,
      /\b(?:users? can|ability to|feature to|main thing|core thing|primary purpose)\s+([^.!?]+)/gi
    ],
    reason: "missing_core_feature" as GateFailureReason,
    weight: 0.9
  },

  successMetrics: {
    patterns: [
      /\b(?:\d+%|\d+\$|percentage|percent|dollar|revenue|conversion|retention|engagement|growth|kpi|metric|measure|success|roi|return)\b/gi,
      /\b(?:success will be measured|we'll know it works|success metric|key metric|performance indicator)\b/gi
    ],
    reason: "missing_success_metrics" as GateFailureReason,
    weight: 0.7
  },

  problemStatement: {
    patterns: [
      /\b(?:problem|issue|challenge|struggle|pain|difficulty|trouble|frustration|obstacle|barrier)\b/gi,
      /\b(?:users? (?:struggle|can't|cannot|have trouble|find it hard|are frustrated))\b/gi
    ],
    reason: "missing_problem_statement" as GateFailureReason,
    weight: 0.8
  }
};

/**
 * Question bank for missing criteria
 */
const CLARIFICATION_QUESTIONS = {
  missing_target_user: [
    "Who exactly is your target user? (e.g., 'busy small business owners', 'freelance designers', 'startup founders')",
    "What type of person or organization would use this product?",
    "Can you describe your ideal customer in more detail?"
  ],
  missing_core_feature: [
    "What is the ONE main thing users will do with your product?",
    "If you had to describe the core feature in one sentence, what would it be?",
    "What's the primary action or capability your product provides?"
  ],
  missing_success_metrics: [
    "How will you measure if this product is successful?",
    "What specific metrics or outcomes would indicate success? (e.g., '20% increase in productivity', '$10k monthly revenue')",
    "What would 'winning' look like for this product?"
  ],
  missing_problem_statement: [
    "What specific problem does this solve for your users?",
    "What pain point or frustration are you addressing?",
    "What struggle do your target users currently face that this product would eliminate?"
  ]
};

/**
 * Analyzes user input for specificity across all criteria
 */
function analyzeSpecificity(userInput: string, previousMessages?: Array<{ content: string }>): {
  isSpecific: boolean;
  missingCriteria: GateFailureReason[];
  confidence: number;
  criteriaScores: Record<string, number>;
} {
  const allText = [userInput, ...(previousMessages?.map(m => m.content) || [])].join(" ");
  const criteriaScores: Record<string, number> = {};
  const missingCriteria: GateFailureReason[] = [];

  // Check each criterion
  for (const [criterionName, criterion] of Object.entries(SPECIFICITY_CRITERIA)) {
    let score = 0;

    for (const pattern of criterion.patterns) {
      const matches = allText.match(pattern);
      if (matches) {
        score = Math.max(score, criterion.weight);
      }
    }

    criteriaScores[criterionName] = score;

    // If score is below threshold, add to missing criteria
    if (score < 0.5) {
      missingCriteria.push(criterion.reason);
    }
  }

  // Calculate overall confidence
  const totalScore = Object.values(criteriaScores).reduce((sum, score) => sum + score, 0);
  const maxPossibleScore = Object.values(SPECIFICITY_CRITERIA).reduce((sum, criterion) => sum + criterion.weight, 0);
  const confidence = totalScore / maxPossibleScore;

  // Consider specific if confidence > 0.8 and no more than 1 missing criterion
  const isSpecific = confidence > 0.8 && missingCriteria.length <= 1;

  return {
    isSpecific,
    missingCriteria,
    confidence,
    criteriaScores
  };
}

/**
 * Generates clarification question based on missing criteria
 */
function generateClarificationQuestion(
  missingCriteria: GateFailureReason[],
  iteration: number
): string {
  if (missingCriteria.length === 0) {
    return "Your request looks good! Let me confirm I have all the details needed.";
  }

  // Pick the most important missing criterion
  const priorityOrder: GateFailureReason[] = [
    "missing_problem_statement",
    "missing_target_user",
    "missing_core_feature",
    "missing_success_metrics"
  ];

  const primaryMissing = priorityOrder.find(criterion => missingCriteria.includes(criterion)) || missingCriteria[0];
  const questions = CLARIFICATION_QUESTIONS[primaryMissing as keyof typeof CLARIFICATION_QUESTIONS] || [];

  // Rotate through questions based on iteration
  const questionIndex = (iteration - 1) % questions.length;
  const question = questions[questionIndex] || questions[0];

  let prefix = "";
  if (iteration === 1) {
    prefix = "I need a bit more detail to help you create the best PRD. ";
  } else if (iteration === 2) {
    prefix = "Thanks for that clarification! I need one more detail: ";
  } else {
    prefix = "Almost there! Just need to clarify: ";
  }

  return prefix + question;
}

/**
 * Specificity Gate function
 *
 * Runs refinement loop to ensure all required criteria are met.
 */
export async function specificityGate(
  ctx: MutationCtx,
  context: GateContext,
  refinementConfig?: {
    maxIterations: number;
    currentIteration: number;
    previousQuestions: string[];
    previousResponses: string[];
  }
): Promise<DetailedGateResult> {
    const startTime = Date.now();
    const config = refinementConfig || {
      maxIterations: 3,
      currentIteration: 1,
      previousQuestions: [],
      previousResponses: []
    };

    try {
      // Analyze specificity including previous messages
      const analysis = analyzeSpecificity(context.userInput, context.previousMessages);

      const processingTime = Date.now() - startTime;

      if (analysis.isSpecific) {
        // All criteria met - PASS
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "specificity",
          status: "pass",
          reasons: [], // Empty array for successful gates
          createdAt: Date.now()
        });

        return {
          status: "pass",
          message: "Perfect! I have all the details needed to create your PRD. All requirements are clear and specific.",
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            iteration: config.currentIteration,
            criteriaScores: JSON.stringify(analysis.criteriaScores),
            missingCriteria: 0
          }
        };
      } else if (config.currentIteration >= config.maxIterations) {
        // Refinement loop exhausted - FAIL
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "specificity",
          status: "fail",
          reasons: ["refinement_loop_exhausted"],
          createdAt: Date.now()
        });

        return {
          status: "fail",
          message: "I've tried to gather more details, but we still need more specificity. Let's proceed with what we have and refine as we go. You can always provide more details later.",
          failureReasons: ["refinement_loop_exhausted"],
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            iteration: config.currentIteration,
            maxIterationsReached: true,
            missingCriteria: analysis.missingCriteria.length
          }
        };
      } else {
        // Need more clarification - ASK
        const clarificationQuestion = generateClarificationQuestion(
          analysis.missingCriteria,
          config.currentIteration
        );

        // Store the current state for next iteration
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "specificity",
          status: "fail",
          reasons: analysis.missingCriteria,
          createdAt: Date.now()
        });

        return {
          status: "fail",
          message: clarificationQuestion,
          failureReasons: analysis.missingCriteria,
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            iteration: config.currentIteration,
            needsRefinement: true,
            missingCriteria: analysis.missingCriteria.length,
            nextIteration: config.currentIteration + 1
          }
        };
      }
    } catch (error) {
      // Log error and return failure
      console.error("Specificity gate error:", error);

      return {
        status: "fail",
        message: "Internal error occurred while analyzing specificity. Please try again.",
        confidence: 0,
        processingTime: Date.now() - startTime,
        metadata: {
          error: "internal_error",
          iteration: config.currentIteration
        }
      };
    }
}
