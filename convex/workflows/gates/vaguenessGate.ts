/**
 * Vagueness Gate - First gate in the cascade flow
 *
 * Detects when user input is too vague to process effectively.
 * Fails when technical jargon, acronyms, or solution-first language appears without context.
 */

import { mutation } from "../../_generated/server";
import { v } from "convex/values";
import type { MutationCtx } from "../../_generated/server";
import {
  type DetailedGateResult,
  type GateContext,
  type GateFailureReason
} from "../sharedTypes";
import { generateClarification } from "../../lib/prompts";

/**
 * Patterns that indicate vagueness or lack of context
 */
const VAGUENESS_PATTERNS = {
  // Technical jargon/acronyms without context
  technicalJargon: {
    pattern: /\b(API|SDK|ML|AI|OCR|ETL|LLM|SaaS|IoT|CRM|ERP|CMS|CDN|REST|GraphQL|JWT|OAuth|CRUD|MVC|MVP|POC|B2B|B2C)\b(?![^()]{0,40}\()/gi,
    reason: "technical_jargon_without_context" as GateFailureReason,
    weight: 0.8
  },

  // Solution-first language (starts with action verbs before stating problem)
  solutionFirst: {
    pattern: /^(Build|Create|Develop|Implement|Make|Design|Add|Write)\s+(?!.*(?:problem|issue|challenge|struggle|pain|difficulty|trouble))/i,
    reason: "solution_first_language" as GateFailureReason,
    weight: 0.7
  },

  // Architecture buzzwords without problem context
  architectureBuzzwords: {
    pattern: /\b(microservice|serverless|event[- ]driven|dockerized|kubernetes|containerized|cloud[- ]native)\b(?!.*(?:problem|issue|challenge|struggle|pain|difficulty))/gi,
    reason: "architecture_buzzwords_without_problem" as GateFailureReason,
    weight: 0.6
  },

  // Too broad or generic requests
  tooGeneric: {
    pattern: /^(help|assist|support|improve|optimize|enhance|fix|update)\s+(me|us|this|that|it)?\s*$/i,
    reason: "too_broad_or_generic" as GateFailureReason,
    weight: 0.9
  },

  // Lacks specific context
  lacksContext: {
    pattern: /\b(this|that|it|they|them)\b(?!\s+(?:is|are|was|were|will|would|should|could|can|may|might))/gi,
    reason: "lacks_specific_context" as GateFailureReason,
    weight: 0.5
  }
};

/**
 * Analyzes user input for vagueness indicators
 */
function analyzeVagueness(userInput: string): {
  isVague: boolean;
  failureReasons: GateFailureReason[];
  confidence: number;
  detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }>;
} {
  // Handle empty or whitespace-only input
  if (!userInput || userInput.trim().length === 0) {
    return {
      isVague: true,
      failureReasons: ["too_broad_or_generic"],
      confidence: 1.0,
      detectedPatterns: [{
        pattern: "empty",
        snippet: userInput,
        reason: "too_broad_or_generic"
      }]
    };
  }

  const detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }> = [];
  let maxWeight = 0;

  // Check each pattern (excluding the overly aggressive lacksContext pattern for now)
  const patternsToCheck = {
    technicalJargon: VAGUENESS_PATTERNS.technicalJargon,
    solutionFirst: VAGUENESS_PATTERNS.solutionFirst,
    architectureBuzzwords: VAGUENESS_PATTERNS.architectureBuzzwords,
    tooGeneric: VAGUENESS_PATTERNS.tooGeneric
  };

  for (const [patternName, config] of Object.entries(patternsToCheck)) {
    const matches = userInput.match(config.pattern);
    if (matches) {
      for (const match of matches) {
        detectedPatterns.push({
          pattern: patternName,
          snippet: match,
          reason: config.reason
        });
        maxWeight = Math.max(maxWeight, config.weight);
      }
    }
  }

  // Calculate confidence and determine if vague
  const confidence = maxWeight;
  const isVague = confidence >= 0.6 || detectedPatterns.length >= 2;

  const failureReasons = [...new Set(detectedPatterns.map(p => p.reason))];

  return {
    isVague,
    failureReasons,
    confidence,
    detectedPatterns
  };
}



/**
 * Vagueness Gate Convex mutation
 *
 * Analyzes user input for vagueness and returns pass/fail result with clarification message if needed.
 * Converts to proper Convex mutation as specified in task requirements.
 */
export const vaguenessGate = mutation({
  args: {
    conversationId: v.id("conversations"),
    message: v.string()
  },
  handler: async (ctx, { conversationId, message }): Promise<DetailedGateResult> => {
    const startTime = Date.now();

    try {
      // Analyze the user input for vagueness
      const analysis = analyzeVagueness(message);

      const processingTime = Date.now() - startTime;

      if (analysis.isVague) {
        // Generate clarification message using lib/prompts
        const clarificationMessage = generateClarification(analysis.failureReasons);

        // Store gate result in database
        await ctx.db.insert("gate_results", {
          conversationId,
          gate: "vagueness",
          status: "fail",
          reasons: analysis.failureReasons,
          createdAt: Date.now()
        });

        return {
          status: "fail",
          message: clarificationMessage,
          failureReasons: analysis.failureReasons,
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            detectedPatterns: analysis.detectedPatterns.length,
            primaryReason: analysis.failureReasons[0] || "unknown"
          }
        };
      } else {
        // Store successful gate result in database
        await ctx.db.insert("gate_results", {
          conversationId,
          gate: "vagueness",
          status: "pass",
          reasons: [], // Empty array for successful gates
          createdAt: Date.now()
        });

        return {
          status: "pass",
          message: "Input has sufficient clarity to proceed.",
          confidence: 1 - analysis.confidence, // Invert confidence for pass case
          processingTime,
          metadata: {
            detectedPatterns: 0,
            primaryReason: "sufficient_clarity"
          }
        };
      }
    } catch (error) {
      // Log error and check if it's a DB write failure
      console.error("Vagueness gate error:", error);

      // If it's a database error, throw specific error for pipeline runner
      if (error instanceof Error && error.message.includes("insert")) {
        throw new Error("DB_WRITE_FAILED");
      }

      return {
        status: "fail",
        message: "Internal error occurred while analyzing your request. Please try again.",
        confidence: 0,
        processingTime: Date.now() - startTime,
        metadata: {
          error: "internal_error"
        }
      };
    }
  }
});

/**
 * Internal helper function for use within the cascade workflow
 * Maintains backward compatibility with existing cascade workflow
 */
export async function vaguenessGateInternal(
  ctx: MutationCtx,
  context: GateContext
): Promise<DetailedGateResult> {
  const startTime = Date.now();

  try {
    // Analyze the user input for vagueness
    const analysis = analyzeVagueness(context.userInput);

    const processingTime = Date.now() - startTime;

    if (analysis.isVague) {
      // Generate clarification message using lib/prompts
      const clarificationMessage = generateClarification(analysis.failureReasons);

      // Store gate result in database
      await ctx.db.insert("gate_results", {
        conversationId: context.conversationId,
        gate: "vagueness",
        status: "fail",
        reasons: analysis.failureReasons,
        createdAt: Date.now()
      });

      return {
        status: "fail",
        message: clarificationMessage,
        failureReasons: analysis.failureReasons,
        confidence: analysis.confidence,
        processingTime,
        metadata: {
          detectedPatterns: analysis.detectedPatterns.length,
          primaryReason: analysis.failureReasons[0] || "unknown"
        }
      };
    } else {
      // Store successful gate result in database
      await ctx.db.insert("gate_results", {
        conversationId: context.conversationId,
        gate: "vagueness",
        status: "pass",
        reasons: [], // Empty array for successful gates
        createdAt: Date.now()
      });

      return {
        status: "pass",
        message: "Input has sufficient clarity to proceed.",
        confidence: 1 - analysis.confidence, // Invert confidence for pass case
        processingTime,
        metadata: {
          detectedPatterns: 0,
          primaryReason: "sufficient_clarity"
        }
      };
    }
  } catch (error) {
    // Log error and check if it's a DB write failure
    console.error("Vagueness gate error:", error);

    // If it's a database error, throw specific error for pipeline runner
    if (error instanceof Error && error.message.includes("insert")) {
      throw new Error("DB_WRITE_FAILED");
    }

    return {
      status: "fail",
      message: "Internal error occurred while analyzing your request. Please try again.",
      confidence: 0,
      processingTime: Date.now() - startTime,
      metadata: {
        error: "internal_error"
      }
    };
  }
}
