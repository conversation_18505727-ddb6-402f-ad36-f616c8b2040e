/**
 * Unit tests for Vagueness Gate
 *
 * Tests the vagueness detection logic with representative PASS and FAIL inputs
 * as specified in task 5.2 requirements.
 */

import { expect, test, describe } from "vitest";
import { vaguenessGateInternal } from "../vaguenessGate";
import type { GateContext } from "../../sharedTypes";

// Helper function to create mock context
function createMockContext() {
  return {
    db: {
      insert: async () => "mock-id",
      query: () => ({ collect: async () => [] }),
      get: async () => null,
      patch: async () => {},
      delete: async () => {}
    }
  } as any;
}

describe("Vagueness Gate", () => {
  const mockConversationId = "test-conversation-id" as any;

  describe("PASS cases - Clear and specific inputs", () => {
    test("should pass for clear problem statement without jargon", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Users forget their passwords too often and need a simple way to reset them without calling support.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("pass");
      expect(result.message).toBe("Input has sufficient clarity to proceed.");
      expect(result.confidence).toBeLessThan(0.6); // Should have low confidence for vagueness
      expect(result.metadata?.detectedPatterns).toBe(0);
    });

    test("should pass for specific feature request with context", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Small business owners struggle to track their inventory manually. They need a simple dashboard to see what's in stock and get alerts when items are running low.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("pass");
      expect(result.message).toBe("Input has sufficient clarity to proceed.");
      expect(result.confidence).toBeLessThan(0.6);
    });

    test("should pass for problem-first approach with clear context", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Remote teams have trouble staying connected and knowing what everyone is working on. We need a way for team members to share quick daily updates without lengthy meetings.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("pass");
      expect(result.message).toBe("Input has sufficient clarity to proceed.");
    });
  });

  describe("FAIL cases - Vague or problematic inputs", () => {
    test("should fail for technical jargon without context", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "We need an AI SaaS that uses LLMs and ML algorithms with REST APIs and JWT authentication.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("fail");
      expect(result.failureReasons).toContain("technical_jargon_without_context");
      expect(result.message).toContain("technical terms that could use more context");
      expect(result.confidence).toBeGreaterThan(0.6);
      expect(result.metadata?.detectedPatterns).toBeGreaterThan(0);
    });

    test("should fail for solution-first language", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Build a dashboard that shows metrics and analytics with charts and graphs.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("fail");
      expect(result.failureReasons).toContain("solution_first_language");
      expect(result.message).toContain("jumping straight to a solution");
      expect(result.message).toContain("describe the problem");
    });

    test("should fail for architecture buzzwords without problem context", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Create a microservice architecture with serverless functions and event-driven processing using Kubernetes.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("fail");
      expect(result.failureReasons).toContain("architecture_buzzwords_without_problem");
      expect(result.message).toContain("technical architecture terms");
      expect(result.message).toContain("specific problem");
    });

    test("should fail for too broad/generic requests", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Help me",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("fail");
      expect(result.failureReasons).toContain("too_broad_or_generic");
      expect(result.message).toContain("quite general");
      expect(result.message).toContain("more specific");
    });

    test("should fail for multiple vagueness patterns", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Build an AI platform that uses ML and APIs to help with this and that.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("fail");
      expect(result.failureReasons?.length).toBeGreaterThan(1);
      expect(result.message).toContain("clarification on a few points");
    });
  });

  describe("Error handling", () => {
    test("should handle empty messages gracefully", async () => {
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "", // Empty message edge case
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBe("fail");
      expect(result.message).toBeDefined();
      expect(typeof result.processingTime).toBe("number");
    });

    test("should handle extremely long messages", async () => {
      const longMessage = "A".repeat(10000); // 10k character message
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: longMessage,
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      const result = await vaguenessGateInternal(mockCtx, context);

      expect(result.status).toBeDefined();
      expect(result.message).toBeDefined();
      expect(typeof result.processingTime).toBe("number");
    });

    test("should throw DB_WRITE_FAILED on database insert errors", async () => {
      const mockCtx = {
        db: {
          insert: async () => {
            throw new Error("Database insert failed");
          }
        }
      } as any;

      const context: GateContext = {
        userInput: "Build an API", // This should fail vagueness check
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      await expect(vaguenessGateInternal(mockCtx, context)).rejects.toThrow("DB_WRITE_FAILED");
    });
  });

  describe("Performance", () => {
    test("should complete within reasonable time", async () => {
      const start = Date.now();
      const mockCtx = createMockContext();
      const context: GateContext = {
        userInput: "Users need help managing their tasks and staying organized throughout the day.",
        conversationId: mockConversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      await vaguenessGateInternal(mockCtx, context);

      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});