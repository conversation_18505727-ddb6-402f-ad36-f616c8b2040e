/**
 * Basic unit tests for Vagueness Gate
 *
 * Minimal tests to verify core functionality.
 * Comprehensive tests will be added in task 5.9.
 */

import { expect, test, describe } from "vitest";
import { vaguenessGateInternal } from "../vaguenessGate";
import type { GateContext } from "../../sharedTypes";

// Helper function to create mock context
function createMockContext() {
  return {
    db: {
      insert: async () => "mock-id",
      get: async () => ({ id: "mock-conversation", userId: "mock-user" }) // Mock conversation exists
    }
  } as any;
}

describe("Vagueness Gate - Basic Tests", () => {
  const mockConversationId = "test-conversation-id" as any;

  test("should pass for clear problem statement", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "Users forget their passwords too often and need a simple way to reset them without calling support.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await vaguenessGateInternal(mockCtx, context);

    expect(result.status).toBe("pass");
    expect(result.message).toBe("Input has sufficient clarity to proceed.");
  });

  test("should fail for technical jargon without context", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "We need an AI SaaS that uses LLMs and ML algorithms with REST APIs.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await vaguenessGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("technical_jargon_without_context");
    expect(result.message).toContain("technical terms");
  });

  test("should fail for solution-first language", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "Build a dashboard that shows metrics and analytics.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await vaguenessGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("solution_first_language");
  });
});