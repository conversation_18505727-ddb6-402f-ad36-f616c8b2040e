/**
 * Enhanced mode detection functions with improved keyword detection and parsing
 * This extends the existing modeDetection.ts with more sophisticated detection logic
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { BasicMessage, detectMode as originalDetectMode } from "./lib/modeDetection";

/**
 * Interface for keyword analysis results
 */
interface KeywordAnalysis {
  prdGenerationScore: number;
  clarificationCompleteScore: number;
  detectedIntent: string;
  prdMatches: Array<{ pattern: string; confidence: number; match: string }>;
  clarificationMatches: Array<{ pattern: string; confidence: number; match: string }>;
}

/**
 * Enhanced keyword patterns for PRD generation detection
 */
interface KeywordPattern {
  pattern: string | RegExp;
  confidence: number; // 0-1 confidence score
  context?: string[]; // Optional context words that boost confidence
}

const PRD_GENERATION_PATTERNS: KeywordPattern[] = [
  // Explicit PRD requests (high confidence)
  { pattern: /\b(generate|create|write|build|make)\s+(the\s+|my\s+)?prd\b/i, confidence: 0.95 },
  { pattern: /\bprd\s+(generation|creation|writing)\b/i, confidence: 0.9 },
  { pattern: /\b(let's|ready to)\s+(generate|create|write|build)\b/i, confidence: 0.85, context: ['prd', 'document'] },

  // Natural language indicators (medium-high confidence)
  { pattern: /\b(i'm|we're)\s+ready\s+(for|to)\b/i, confidence: 0.8, context: ['generate', 'create', 'prd'] },
  { pattern: /\b(let's\s+)?(get\s+)?started\s+(with|on)\b/i, confidence: 0.75, context: ['prd', 'generation', 'document'] },
  { pattern: /\bnow\s+(generate|create|write|build)\b/i, confidence: 0.8 },

  // Action-oriented phrases (medium confidence)
  { pattern: /\b(go\s+ahead|proceed)\s+(and|with|to)\b/i, confidence: 0.7, context: ['generate', 'create'] },
  { pattern: /\b(that's\s+)?(enough|sufficient|good)\s+(clarification|information)\b/i, confidence: 0.75 },
  { pattern: /\ball\s+set\b/i, confidence: 0.7, context: ['generate', 'prd'] },

  // Refinement patterns
  { pattern: /\b(refine|improve|update|modify|revise|edit|adjust|fix)\s+(the\s+)?prd\b/i, confidence: 0.9 },
  { pattern: /\bchange\s+(this|that|it)\b/i, confidence: 0.6, context: ['prd', 'document'] },
];

const CLARIFICATION_COMPLETE_PATTERNS: KeywordPattern[] = [
  // Strong completion indicators
  { pattern: /\b(that's\s+)?(exactly|perfect|correct|right)\b/i, confidence: 0.9 },
  { pattern: /\b(yes,?\s+)?(that's\s+it|that\s+works)\b/i, confidence: 0.85 },
  { pattern: /\bsounds\s+(good|great|perfect)\b/i, confidence: 0.8 },

  // Confirmation patterns
  { pattern: /\bi\s+agree\b/i, confidence: 0.75 },
  { pattern: /\b(that's\s+)?(clear|understood)\b/i, confidence: 0.7 },
  { pattern: /\bmakes\s+sense\b/i, confidence: 0.7 },
];

/**
 * Enhanced keyword detection with confidence scoring
 */
export const detectKeywords = query({
  args: {
    message: v.string(),
    conversationHistory: v.array(v.object({
      role: v.string(),
      content: v.string(),
    })),
  },
  handler: async (ctx, args): Promise<{
    prdGenerationScore: number;
    clarificationCompleteScore: number;
    prdMatches: { pattern: string; confidence: number; match: string }[];
    clarificationMatches: { pattern: string; confidence: number; match: string }[];
    detectedIntent: string;
  }> => {
    const { message, conversationHistory } = args;
    const lowercaseMessage = message.toLowerCase();

    // Calculate context from conversation history
    const conversationText = conversationHistory
      .map(msg => msg.content.toLowerCase())
      .join(' ');

    // Detect PRD generation intent
    let prdGenerationScore = 0;
    const prdMatches: { pattern: string; confidence: number; match: string }[] = [];

    for (const { pattern, confidence, context } of PRD_GENERATION_PATTERNS) {
      const regex = typeof pattern === 'string' ? new RegExp(pattern, 'i') : pattern;
      const match = lowercaseMessage.match(regex);

      if (match) {
        let adjustedConfidence = confidence;

        // Boost confidence if context words are present
        if (context) {
          const contextBoost = context.filter(word =>
            conversationText.includes(word) || lowercaseMessage.includes(word)
          ).length / context.length;
          adjustedConfidence = Math.min(1, confidence + (contextBoost * 0.2));
        }

        prdGenerationScore = Math.max(prdGenerationScore, adjustedConfidence);
        prdMatches.push({
          pattern: pattern.toString(),
          confidence: adjustedConfidence,
          match: match[0]
        });
      }
    }

    // Detect clarification completion
    let clarificationCompleteScore = 0;
    const clarificationMatches: { pattern: string; confidence: number; match: string }[] = [];

    for (const { pattern, confidence } of CLARIFICATION_COMPLETE_PATTERNS) {
      const regex = typeof pattern === 'string' ? new RegExp(pattern, 'i') : pattern;
      const match = lowercaseMessage.match(regex);

      if (match) {
        clarificationCompleteScore = Math.max(clarificationCompleteScore, confidence);
        clarificationMatches.push({
          pattern: pattern.toString(),
          confidence,
          match: match[0]
        });
      }
    }

    return {
      prdGenerationScore,
      clarificationCompleteScore,
      prdMatches,
      clarificationMatches,
      detectedIntent: prdGenerationScore > 0.7 ? 'prd_generation' :
                     clarificationCompleteScore > 0.6 ? 'clarification_complete' :
                     'continue_clarification'
    };
  },
});

/**
 * Enhanced mode detection that combines original logic with new keyword detection
 */
export const detectEnhancedMode = query({
  args: {
    messages: v.array(v.object({
      role: v.string(),
      content: v.string(),
    })),
  },
  handler: async (ctx, args): Promise<{
    mode: 'clarification' | 'generation' | 'refinement';
    confidence: number;
    reasoning: string;
    keywordAnalysis?: KeywordAnalysis;
  }> => {
    const { messages } = args;

    if (!messages || messages.length === 0) {
      return {
        mode: 'clarification' as const,
        confidence: 1.0,
        reasoning: 'No messages to analyze'
      };
    }

    // Get the original mode detection result
    const originalMode = originalDetectMode(messages as BasicMessage[]);

    // Get enhanced keyword detection for the last user message
    const lastUserMessage = messages
      .filter(msg => msg.role === 'user')
      .pop();

    if (!lastUserMessage) {
      return {
        mode: originalMode,
        confidence: 0.5,
        reasoning: 'No user messages found'
      };
    }

    // Get enhanced keyword detection for the last user message
    const messageText = lastUserMessage.content.toLowerCase();
    const conversationText = messages.map(msg => msg.content.toLowerCase()).join(' ');

    // Calculate PRD generation score directly to avoid circular dependency
    let prdGenerationScore = 0;
    const prdMatches: { pattern: string; confidence: number; match: string }[] = [];

    for (const { pattern, confidence, context } of PRD_GENERATION_PATTERNS) {
      const regex = typeof pattern === 'string' ? new RegExp(pattern, 'i') : pattern;
      const match = messageText.match(regex);

      if (match) {
        let adjustedConfidence = confidence;

        if (context) {
          const contextBoost = context.filter(word =>
            conversationText.includes(word) || messageText.includes(word)
          ).length / context.length;
          adjustedConfidence = Math.min(1, confidence + (contextBoost * 0.2));
        }

        prdGenerationScore = Math.max(prdGenerationScore, adjustedConfidence);
        prdMatches.push({
          pattern: pattern.toString(),
          confidence: adjustedConfidence,
          match: match[0]
        });
      }
    }

    const keywordResults = {
      prdGenerationScore,
      clarificationCompleteScore: 0, // Simplified for now
      detectedIntent: prdGenerationScore > 0.7 ? 'prd_generation' : 'continue_clarification',
      prdMatches,
      clarificationMatches: []
    };

    // Combine original logic with enhanced detection
    let finalMode = originalMode;
    let confidence = 0.7; // Base confidence in original mode
    let reasoning = `Original mode detection: ${originalMode}`;

    // Override with enhanced detection if confidence is high enough
    if (keywordResults.prdGenerationScore > 0.8) {
      finalMode = 'generation';
      confidence = keywordResults.prdGenerationScore;
      reasoning = `High confidence PRD generation request detected (${keywordResults.prdGenerationScore.toFixed(2)})`;
    } else if (keywordResults.detectedIntent === 'prd_generation' && originalMode === 'generation') {
      confidence = Math.max(confidence, keywordResults.prdGenerationScore);
      reasoning = `Both original and enhanced detection agree on generation mode`;
    }

    return {
      mode: finalMode,
      confidence,
      reasoning,
      keywordAnalysis: keywordResults
    };
  },
});

/**
 * Parse mode transition requests from user messages
 */
export const parseModeTransition = query({
  args: {
    message: v.string(),
    currentMode: v.union(v.literal("clarification"), v.literal("generation"), v.literal("refinement")),
  },
  handler: async (ctx, args): Promise<{
    requestedMode: 'clarification' | 'generation' | 'refinement';
    confidence: number;
    trigger: string;
    isExplicitCommand: boolean;
  }> => {
    const { message, currentMode } = args;
    const lowercaseMessage = message.toLowerCase();

    // Explicit mode switching commands
    const modeCommands = {
      clarification: [/\/clarify/i, /back to clarification/i, /more questions/i],
      generation: [/\/generate/i, /generate prd/i, /create prd/i],
      refinement: [/\/refine/i, /refine prd/i, /improve prd/i]
    };

    for (const [targetMode, patterns] of Object.entries(modeCommands)) {
      for (const pattern of patterns) {
        if (pattern.test(lowercaseMessage)) {
          return {
            requestedMode: targetMode as 'clarification' | 'generation' | 'refinement',
            confidence: 0.95,
            trigger: pattern.source,
            isExplicitCommand: true
          };
        }
      }
    }

    // Implicit mode transitions based on context
    if (currentMode === 'clarification') {
      // Calculate PRD generation score directly to avoid circular dependency
      const messageText = message.toLowerCase();
      let prdGenerationScore = 0;

      for (const { pattern, confidence } of PRD_GENERATION_PATTERNS) {
        const regex = typeof pattern === 'string' ? new RegExp(pattern, 'i') : pattern;
        if (regex.test(messageText)) {
          prdGenerationScore = Math.max(prdGenerationScore, confidence);
        }
      }

      if (prdGenerationScore > 0.7) {
        return {
          requestedMode: 'generation' as const,
          confidence: prdGenerationScore,
          trigger: 'prd_generation_keywords',
          isExplicitCommand: false
        };
      }
    }

    return {
      requestedMode: currentMode,
      confidence: 0.1,
      trigger: 'no_transition_detected',
      isExplicitCommand: false
    };
  },
});

/**
 * Store mode detection results for analytics and improvement
 */
export const logModeDetection = mutation({
  args: {
    conversationId: v.id("conversations"),
    message: v.string(),
    detectedMode: v.union(v.literal("clarification"), v.literal("generation"), v.literal("refinement")),
    confidence: v.number(),
    keywordAnalysis: v.object({
      prdGenerationScore: v.number(),
      clarificationCompleteScore: v.number(),
      detectedIntent: v.string(),
    }),
  },
  handler: async (ctx, args): Promise<{ logged: boolean }> => {
    // Store mode detection results for future analysis and improvement
    await ctx.db.insert("modeDetectionLogs", {
      conversationId: args.conversationId,
      message: args.message,
      detectedMode: args.detectedMode,
      confidence: args.confidence,
      keywordAnalysis: args.keywordAnalysis,
      timestamp: Date.now(),
    });

    return { logged: true };
  },
});