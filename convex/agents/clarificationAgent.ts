import { Agent } from "@convex-dev/agent";
import { components } from "../_generated/api";
import { v } from "convex/values";
import { action } from "../_generated/server";
import { openai } from "@ai-sdk/openai";
import { extractUnixCoreObjective, UnixCoreObjective } from "../lib/jsonExtraction";

// Scope-creep detection keywords
const SCOPE_CREEP_KEYWORDS = [
  "also", "while", "multiple", "dashboard", "platform", "and", "plus",
  "additionally", "furthermore", "moreover", "as well as", "including",
  "along with", "together with", "combined with", "integrated with"
];

// UnixCoreObjective type is now imported from ../lib/jsonExtraction



// Create the Clarification Specialist Agent
const clarificationAgent = new Agent(components.agent, {
  name: "Clarification Specialist",
  chat: openai("gpt-4o-mini"),
  instructions: `You are the Clarification Specialist Agent - a Unix philosophy enforcer for PRD generation.

🎯 CORE MISSION: Guarantee every feature is reduced to ONE clearly articulated purpose before PRD generation.

🔒 UNIX PHILOSOPHY ENFORCEMENT:
- "Do one thing and do it well" - this is your mantra
- REFUSE vague inputs with "STOP. Too vague."
- Push back aggressively on scope creep with "SCOPE CREEP DETECTION!"
- Guide users through systematic clarification rounds

📋 INTERACTION PATTERN:
1. Receive raw feature description
2. Ask: "What is the single outcome this feature must deliver exceptionally well?"
3. Detect scope creep - if found, trigger follow-up questions
4. Present additional functions detected; request user confirmation to remove/defer/split
5. Iterate until one-sentence Unix Core Objective is finalized (≤140 chars)
6. Pass sanitized feature + decision log to downstream

🚨 SCOPE CREEP TRIGGERS:
- Words like "also," "while," "multiple," "dashboard," "platform"
- Multiple functional domains in one description
- Feature creep indicators

⚡ STATUS SYSTEM:
- "[Clarification Engine: ACTIVE]" - when analyzing
- "[Focus Validator: ARMED]" - when checking scope
- "[Definition Lock: PENDING]" - when almost ready
- "🔒 DEFINITION LOCKED!" - when objective finalized

🎉 CELEBRATION PATTERNS:
- "EXCELLENT! 🎯" when user provides clear focus
- "🔒 DEFINITION LOCKED!" when objective is finalized
- "Unix philosophy achieved! ✨" when ready for handoff

🔍 SCOPE CREEP DETECTION:
Automatically detect these keywords: ${SCOPE_CREEP_KEYWORDS.join(", ")}
When detected, immediately respond with "🚨 SCOPE CREEP DETECTION!" and guide user to focus.

TARGET: Complete clarification in ≤3 turns, 80% of the time. Be relentless but helpful.`,

  maxSteps: 8, // Reduced since no tool calls needed
  maxRetries: 3,
});

// Export the agent as Convex actions for workflow integration
export const createClarificationThread = clarificationAgent.createThreadMutation();

export const clarifyFeature = clarificationAgent.asTextAction({
  maxSteps: 8,
});

export const streamClarification = action({
  args: {
    threadId: v.string(),
    prompt: v.string(),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, { threadId, prompt }) => {
    try {
      const { thread } = await clarificationAgent.continueThread(ctx, { threadId });

      const result = await thread.streamText(
        { prompt },
        {
          saveStreamDeltas: false // Only save final complete message
        }
      );

      await result.consumeStream();
      return { success: true, threadId };
    } catch (error) {
      console.error('Error in clarification streaming:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        threadId
      };
    }
  },
});

// Standalone clarification action for workflow integration
export const processClarification = action({
  args: {
    featureDescription: v.string(),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, { featureDescription, userId }) => {
    try {
      // Create a new thread for this clarification session
      const { thread } = await clarificationAgent.createThread(ctx, {
        userId: userId || "anonymous",
        title: "Feature Clarification Session"
      });

      // Process the feature description through clarification
      const result = await thread.generateText({
        prompt: `Please analyze this feature request and guide me through clarification: "${featureDescription}"

        Follow your systematic clarification process:
        1. Detect any scope creep
        2. Identify the single core purpose
        3. List any functions that should be removed/deferred
        4. Generate a Unix Core Objective (≤140 chars)
        5. Determine if ready for PRD generation

        Return ONLY a valid JSON object with the following structure:
        {
          "objective": "Single, clear purpose statement (≤140 chars)",
          "confidence": 0.8,
          "scopeCreepDetected": true/false,
          "removedFunctions": ["function1", "function2"],
          "clarificationTurns": 1,
          "readyForPRD": true/false
        }`
      });

      // Parse the JSON response using robust extraction
      let clarification: UnixCoreObjective;
      try {
        // Extract JSON from response (handles cases where LLM adds text around JSON)
        clarification = extractUnixCoreObjective(result.text);
      } catch (parseError) {
        throw new Error(`Failed to parse clarification response: ${parseError}`);
      }

      return {
        success: true,
        threadId: thread.threadId,
        clarification,
        message: clarification.readyForPRD
          ? "🔒 DEFINITION LOCKED! Ready for PRD generation."
          : "🔄 Additional clarification needed."
      };
    } catch (error) {
      console.error('Error in processClarification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clarification failed',
        clarification: null
      };
    }
  },
});
