/**
 * Test data and utilities for Clarification Specialist Agent
 *
 * Contains test cases, mock data, and helper functions for testing
 * the Unix philosophy enforcement, scope-creep detection,
 * and clarification workflow functionality.
 */

// Test data for scope creep detection
export const SCOPE_CREEP_TEST_CASES = [
  {
    description: "I want a user dashboard that also shows analytics and manages user profiles",
    expectedScopeCreep: true,
    expectedKeywords: ["also", "and"],
    expectedFunctions: ["user dashboard", "analytics", "user profile management"]
  },
  {
    description: "Build a simple login form for user authentication",
    expectedScopeCreep: false,
    expectedKeywords: [],
    expectedFunctions: ["user authentication"]
  },
  {
    description: "Create a platform that handles multiple payment methods while integrating with various APIs",
    expectedScopeCreep: true,
    expectedKeywords: ["platform", "multiple", "while"],
    expectedFunctions: ["payment processing", "API integration"]
  },
  {
    description: "I need a search feature plus filtering and sorting capabilities",
    expectedScopeCreep: true,
    expectedKeywords: ["plus", "and"],
    expectedFunctions: ["search", "filtering", "sorting"]
  },
  {
    description: "Design a notification system",
    expectedScopeCreep: false,
    expectedKeywords: [],
    expectedFunctions: ["notification system"]
  }
];

// Test data for Unix Core Objective generation
export const UNIX_OBJECTIVE_TEST_CASES = [
  {
    input: "I want a user dashboard that also shows analytics and manages user profiles",
    expectedObjective: "Enable users to view their account information and activity status",
    maxLength: 140,
    shouldRequireClarification: true
  },
  {
    input: "Build a simple login form for user authentication",
    expectedObjective: "Authenticate users securely through email and password verification",
    maxLength: 140,
    shouldRequireClarification: false
  },
  {
    input: "Create a platform that handles multiple payment methods while integrating with various APIs",
    expectedObjective: "Process customer payments securely using credit cards",
    maxLength: 140,
    shouldRequireClarification: true
  }
];

// Test data for clarification conversation flow
export const CLARIFICATION_FLOW_TEST_CASES = [
  {
    name: "Scope creep detection and resolution",
    initialInput: "I want a user dashboard that also shows analytics and manages user profiles",
    expectedFlow: [
      {
        agentResponse: "🚨 SCOPE CREEP DETECTION! I detected multiple functions in your request",
        userResponse: "I mainly need users to see their profile information"
      },
      {
        agentResponse: "What is the single outcome this feature must deliver exceptionally well?",
        userResponse: "Users should be able to view and edit their profile details"
      },
      {
        agentResponse: "🔒 DEFINITION LOCKED! Unix Core Objective: Enable users to view and update their profile information",
        expectedResult: {
          objective: "Enable users to view and update their profile information",
          readyForPRD: true,
          clarificationTurns: 2
        }
      }
    ]
  },
  {
    name: "Clear input requiring minimal clarification",
    initialInput: "Build a simple login form for user authentication",
    expectedFlow: [
      {
        agentResponse: "✅ No obvious scope creep detected",
        expectedResult: {
          objective: "Authenticate users securely through email and password verification",
          readyForPRD: true,
          clarificationTurns: 1
        }
      }
    ]
  }
];

// Performance benchmarks
export const PERFORMANCE_BENCHMARKS = {
  maxClarificationTurns: 3,
  targetCompletionRate: 0.8, // 80% of sessions complete in ≤3 turns
  maxResponseTime: 5000, // 5 seconds max response time
  scopeCreepDetectionAccuracy: 0.9, // 90% accuracy
  objectiveMaxLength: 140 // characters
};

// Mock conversation contexts for testing
export const MOCK_CONVERSATION_CONTEXTS = [
  {
    threadId: "test-thread-1",
    userId: "test-user-1",
    conversationHistory: [
      { role: "user", content: "I want to build a feature" },
      { role: "assistant", content: "What specific problem does this feature solve?" }
    ]
  },
  {
    threadId: "test-thread-2",
    userId: "anonymous",
    conversationHistory: []
  }
];

// Expected decision log entries for testing
export const EXPECTED_DECISION_LOGS = [
  {
    originalFunction: "analytics dashboard",
    decision: "separate_ticket" as const,
    reason: "Analytics is a separate functional domain from user profile management"
  },
  {
    originalFunction: "user profile management",
    decision: "deferred" as const,
    reason: "Management features add complexity; focus on viewing first"
  },
  {
    originalFunction: "multiple payment methods",
    decision: "removed" as const,
    reason: "Start with single payment method to reduce complexity"
  }
];

// Test helper functions
export const TEST_HELPERS = {
  /**
   * Validate Unix Core Objective format
   */
  validateUnixObjective: (objective: string): boolean => {
    return objective.length <= 140 &&
           objective.length > 10 &&
           !objective.includes("and") &&
           !objective.includes("also") &&
           objective.trim() === objective;
  },

  /**
   * Count scope creep keywords in text
   */
  countScopeCreepKeywords: (text: string): number => {
    const keywords = ["also", "while", "multiple", "dashboard", "platform", "and", "plus"];
    return keywords.filter(keyword =>
      text.toLowerCase().includes(keyword.toLowerCase())
    ).length;
  },

  /**
   * Simulate clarification conversation with realistic agent responses
   * NOTE: This is a mock simulation for testing purposes. In actual integration tests,
   * this should call the real clarification agent.
   */
  simulateConversation: async (initialInput: string, maxTurns = 3) => {
    const turns = [];
    let currentInput = initialInput;
    let turnCount = 0;
    let objectiveClarity = 0.3; // Start with low clarity

    while (turnCount < maxTurns && objectiveClarity < 0.8) {
      const scopeCreepDetected = TEST_HELPERS.countScopeCreepKeywords(currentInput) > 0;

      // Generate realistic mock agent response based on input analysis
      let agentResponse: string;
      if (turnCount === 0 && scopeCreepDetected) {
        agentResponse = "🚨 SCOPE CREEP DETECTION! I detected multiple functions in your request. What is the single outcome this feature must deliver exceptionally well?";
      } else if (turnCount === 0 && !scopeCreepDetected) {
        agentResponse = "✅ No obvious scope creep detected. Can you clarify the primary user outcome this feature should achieve?";
      } else if (objectiveClarity < 0.6) {
        agentResponse = "I need more specificity. What is the ONE thing users should be able to accomplish with this feature?";
      } else {
        agentResponse = "🔒 DEFINITION LOCKED! Unix Core Objective generated and ready for PRD.";
      }

      turns.push({
        turn: turnCount + 1,
        input: currentInput,
        response: agentResponse,
        scopeCreepDetected,
        objectiveClarity
      });

      turnCount++;

      // Simulate user refinement for next iteration
      if (turnCount < maxTurns && objectiveClarity < 0.8) {
        currentInput = TEST_HELPERS.generateMockUserRefinement(currentInput, agentResponse);
        objectiveClarity += 0.3; // Improve clarity with each iteration
      }
    }

    return {
      totalTurns: turnCount,
      conversation: turns,
      completed: objectiveClarity >= 0.8,
      finalObjectiveClarity: objectiveClarity
    };
  },

  /**
   * Generate mock user refinement based on agent feedback
   */
  generateMockUserRefinement: (originalInput: string, agentResponse: string): string => {
    // Remove scope creep keywords and focus on single function
    const refinedInput = originalInput
      .replace(/\b(also|while|multiple|platform|and|plus|additionally)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Add focus based on agent response type
    if (agentResponse.includes('SCOPE CREEP')) {
      return `I want to focus on just the core ${refinedInput.split(' ').slice(-2).join(' ')} functionality`;
    } else if (agentResponse.includes('primary user outcome')) {
      return `Users need to ${refinedInput.toLowerCase().replace(/^i want|^create|^build/, '').trim()}`;
    } else {
      return refinedInput;
    }
  }
};

// Integration test scenarios
export const INTEGRATION_TEST_SCENARIOS = [
  {
    name: "End-to-end clarification workflow",
    description: "Test complete flow from vague input to Unix Core Objective",
    steps: [
      "Submit vague feature request",
      "Detect scope creep",
      "Guide user through clarification",
      "Generate Unix Core Objective",
      "Save decision log",
      "Mark ready for PRD generation"
    ]
  },
  {
    name: "Error handling and recovery",
    description: "Test agent behavior with invalid inputs and errors",
    steps: [
      "Submit empty input",
      "Submit extremely long input",
      "Test network failures",
      "Test database errors",
      "Verify graceful degradation"
    ]
  },
  {
    name: "Performance under load",
    description: "Test agent performance with concurrent requests",
    steps: [
      "Submit multiple concurrent clarification requests",
      "Measure response times",
      "Verify accuracy under load",
      "Check resource usage"
    ]
  }
];

// Export all test data for use in actual test files
const testData = {
  SCOPE_CREEP_TEST_CASES,
  UNIX_OBJECTIVE_TEST_CASES,
  CLARIFICATION_FLOW_TEST_CASES,
  PERFORMANCE_BENCHMARKS,
  MOCK_CONVERSATION_CONTEXTS,
  EXPECTED_DECISION_LOGS,
  TEST_HELPERS,
  INTEGRATION_TEST_SCENARIOS
};

export default testData;
