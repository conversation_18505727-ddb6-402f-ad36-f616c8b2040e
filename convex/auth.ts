import { convexAuth } from "@convex-dev/auth/server";
import GitHub from "@auth/core/providers/github";
import Google from "@auth/core/providers/google";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    GitHub({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    }),
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
  ],
  callbacks: {
    async createOrUpdateUser(ctx, args) {
      // If user already exists, return their ID
      if (args.existingUserId) {
        // Update lastActiveAt for existing users
        await ctx.db.patch(args.existingUserId, {
          lastActiveAt: Date.now(),
        });
        return args.existingUserId;
      }

      // Create new user with all required fields
      return await ctx.db.insert("users", {
        email: args.profile.email,
        name: args.profile.name,
        tokenIdentifier: args.profile.sub || args.profile.id || args.profile.email!,
        totalPRDsGenerated: 0,
        planType: "free",
        createdAt: Date.now(),
        lastActiveAt: Date.now(),
      });
    },
  },
});
