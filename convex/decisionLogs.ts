import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Shared validation schema for decision log entries
const decisionLogEntrySchema = v.object({
  threadId: v.string(),
  originalFunction: v.string(),
  decision: v.union(
    v.literal("removed"),
    v.literal("deferred"),
    v.literal("separate_ticket")
  ),
  reason: v.string(),
  featureId: v.optional(v.string()),
  userId: v.optional(v.string()),
});

// Decision log entry for tracking removed/deferred functions
export const saveDecisionLogEntry = mutation({
  args: decisionLogEntrySchema,
  handler: async (ctx, args) => {
    const entryId = await ctx.db.insert("decision_logs", {
      threadId: args.threadId,
      originalFunction: args.originalFunction,
      decision: args.decision,
      reason: args.reason,
      featureId: args.featureId,
      userId: args.userId || "anonymous",
      timestamp: Date.now(),
      createdAt: Date.now(),
    });

    return entryId;
  },
});

// Save multiple decision log entries in batch
export const saveBatchDecisionLogs = mutation({
  args: {
    entries: v.array(decisionLogEntrySchema),
  },
  handler: async (ctx, { entries }) => {
    const timestamp = Date.now();
    const entryIds = [];

    for (const entry of entries) {
      const entryId = await ctx.db.insert("decision_logs", {
        ...entry,
        userId: entry.userId || "anonymous",
        timestamp,
        createdAt: timestamp,
      });
      entryIds.push(entryId);
    }

    return {
      success: true,
      entriesCreated: entryIds.length,
      entryIds,
    };
  },
});

// Get decision logs for a specific thread/clarification session
export const getDecisionLogsForThread = query({
  args: {
    threadId: v.string(),
  },
  handler: async (ctx, { threadId }) => {
    const logs = await ctx.db
      .query("decision_logs")
      .filter((q) => q.eq(q.field("threadId"), threadId))
      .order("desc")
      .collect();

    return logs;
  },
});

// Get decision logs for a specific feature
export const getDecisionLogsForFeature = query({
  args: {
    featureId: v.string(),
  },
  handler: async (ctx, { featureId }) => {
    const logs = await ctx.db
      .query("decision_logs")
      .filter((q) => q.eq(q.field("featureId"), featureId))
      .order("desc")
      .collect();

    return logs;
  },
});

// Get all decision logs for analytics
export const getAllDecisionLogs = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 100 }) => {
    const logs = await ctx.db
      .query("decision_logs")
      .order("desc")
      .take(limit);

    return logs;
  },
});

// Get decision log statistics
export const getDecisionLogStats = query({
  args: {
    timeRangeMs: v.optional(v.number()), // Time range in milliseconds
  },
  handler: async (ctx, { timeRangeMs }) => {
    // Validate timeRangeMs - must be positive if provided
    if (timeRangeMs !== undefined && timeRangeMs <= 0) {
      throw new Error("timeRangeMs must be a positive number representing milliseconds");
    }

    const cutoffTime = timeRangeMs ? Date.now() - timeRangeMs : 0;

    const logs = await ctx.db
      .query("decision_logs")
      .filter((q) => q.gte(q.field("timestamp"), cutoffTime))
      .collect();

    const stats = {
      totalEntries: logs.length,
      decisionBreakdown: {
        removed: logs.filter(log => log.decision === "removed").length,
        deferred: logs.filter(log => log.decision === "deferred").length,
        separate_ticket: logs.filter(log => log.decision === "separate_ticket").length,
      },
      uniqueThreads: new Set(logs.map(log => log.threadId)).size,
      uniqueFeatures: new Set(logs.map(log => log.featureId).filter(Boolean)).size,
      timeRange: timeRangeMs ? `${Math.round((timeRangeMs / (1000 * 60 * 60 * 24)) * 100) / 100} days` : "all time",
    };

    return stats;
  },
});

// Delete decision logs for a thread (cleanup)
export const deleteDecisionLogsForThread = mutation({
  args: {
    threadId: v.string(),
  },
  handler: async (ctx, { threadId }) => {
    const logs = await ctx.db
      .query("decision_logs")
      .filter((q) => q.eq(q.field("threadId"), threadId))
      .collect();

    let deletedCount = 0;
    for (const log of logs) {
      await ctx.db.delete(log._id);
      deletedCount++;
    }

    return {
      success: true,
      deletedCount,
    };
  },
});
