import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { components } from "./_generated/api";
import { listMessages } from "@convex-dev/agent";

// Create a new conversation thread
export const createThread = mutation({
  args: {
    title: v.optional(v.string()),
  },
  handler: async (ctx, { title }) => {
    // Get authenticated user ID (can be null for anonymous users)
    const authenticatedUserId = await getAuthUserId(ctx);

    const threadId = await ctx.db.insert("threads", {
      userId: authenticatedUserId || "anonymous",
      title: title || "New Conversation",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return threadId;
  },
});

// Get all threads for a user
export const getUserThreads = query({
  args: {},
  handler: async (ctx) => {
    // Get authenticated user ID (can be null for anonymous users)
    const authenticatedUserId = await getAuthUserId(ctx);
    const userId = authenticatedUserId || "anonymous";

    return await ctx.db
      .query("threads")
      .filter((q) => q.eq(q.field("userId"), userId))
      .order("desc")
      .collect();
  },
});

// Get a specific thread
export const getThread = query({
  args: {
    threadId: v.id("threads"),
  },
  handler: async (ctx, { threadId }) => {
    // Require authentication
    const authenticatedUserId = await getAuthUserId(ctx);
    if (authenticatedUserId === null) {
      throw new Error("Authentication required");
    }

    const thread = await ctx.db.get(threadId);
    if (!thread) {
      return null;
    }

    // Verify ownership - threads with userId of "anonymous" can only be accessed by anonymous users
    // and threads with actual user IDs can only be accessed by that user
    if (thread.userId !== "anonymous" && thread.userId !== authenticatedUserId) {
      throw new Error("Unauthorized: access denied to thread");
    }

    return thread;
  },
});

// Update thread title
export const updateThreadTitle = mutation({
  args: {
    threadId: v.id("threads"),
    title: v.string(),
  },
  handler: async (ctx, { threadId, title }) => {
    // Require authentication
    const authenticatedUserId = await getAuthUserId(ctx);
    if (authenticatedUserId === null) {
      throw new Error("Authentication required");
    }

    const thread = await ctx.db.get(threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    // Verify ownership
    if (thread.userId !== "anonymous" && thread.userId !== authenticatedUserId) {
      throw new Error("Unauthorized: access denied to thread");
    }

    await ctx.db.patch(threadId, {
      title,
      updatedAt: Date.now(),
    });
  },
});

// Delete a thread
export const deleteThread = mutation({
  args: {
    threadId: v.id("threads"),
  },
  handler: async (ctx, { threadId }) => {
    // Require authentication
    const authenticatedUserId = await getAuthUserId(ctx);
    if (authenticatedUserId === null) {
      throw new Error("Authentication required");
    }

    const thread = await ctx.db.get(threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    // Verify ownership
    if (thread.userId !== "anonymous" && thread.userId !== authenticatedUserId) {
      throw new Error("Unauthorized: access denied to thread");
    }

    // Cascade deletion: Delete all associated messages first
    try {
      // Get all messages for this thread
      const messages = await listMessages(ctx, components.agent, {
        threadId: threadId,
        paginationOpts: { numItems: 1000, cursor: null }, // Get up to 1000 messages
      });

      // Delete all messages associated with this thread
      // Note: The @convex-dev/agent component handles message storage internally
      // We need to call the agent's cleanup function if available, or
      // the messages will be orphaned but this is acceptable for now
      // TODO: Implement proper cascade deletion when agent component API supports it

      // For now, we document that message cleanup should be handled
      // by the agent component's garbage collection or manual cleanup
      console.warn(`Thread ${threadId} deleted but ${messages.page.length} associated messages may be orphaned`);

    } catch (error) {
      // If we can't clean up messages, we still delete the thread
      // but log the issue for monitoring
      console.error(`Failed to clean up messages for thread ${threadId}:`, error);
    }

    // Delete the thread
    await ctx.db.delete(threadId);
  },
});