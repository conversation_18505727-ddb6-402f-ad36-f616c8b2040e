import { ConvexError } from "convex/values";
import { MutationCtx, QueryCtx, ActionCtx } from "../_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "../_generated/dataModel";

/**
 * Structured error logging
 */
interface ErrorLogEntry {
  functionName: string;
  userId?: Id<"users">;
  sessionId?: string;
  inputParams: Record<string, unknown>;
  error: Error;
  timestamp: number;
}

/**
 * Log error to the database for monitoring
 */
async function logError(
  ctx: MutationCtx,
  entry: ErrorLogEntry
): Promise<void> {
  try {
    await ctx.db.insert("errorLogs", {
      errorType: entry.error.name || "UnknownError",
      errorMessage: entry.error.message,
      stackTrace: entry.error.stack,
      userId: entry.userId,
      route: entry.functionName,
      method: "CONVEX_FUNCTION",
      severity: "high",
      resolved: false,
      createdAt: entry.timestamp,
      conversationId: typeof entry.inputParams?.conversationId === 'string' &&
        /^[a-zA-Z0-9_-]+$/.test(entry.inputParams.conversationId) &&
        entry.inputParams.conversationId.length > 0
        ? entry.inputParams.conversationId as Id<"conversations">
        : undefined,
    });
  } catch (logError) {
    // Don't let logging errors break the main flow
    console.error("Failed to log error to database:", logError);
  }
}

/**
 * Converts a generic error into a standardized ConvexError with an appropriate code, message, and HTTP status.
 *
 * Maps known error message patterns to specific error codes and statuses; unknown errors are returned as internal errors.
 *
 * @param error - The error to convert
 * @returns A ConvexError object with code, message, and status fields
 */
function createConvexError(error: Error): ConvexError<{
  code: string;
  message: string;
  status: number;
}> {
  // Handle known error types
  if (error.message.includes("Rate limit exceeded")) {
    return new ConvexError({
      code: "RATE_LIMITED",
      message: "Too many requests. Please try again later.",
      status: 429
    });
  }

  if (error.message.includes("Not authenticated") || error.message.includes("Access denied")) {
    return new ConvexError({
      code: "UNAUTHORIZED",
      message: "Authentication required",
      status: 401
    });
  }

  if (error.message.includes("not found")) {
    return new ConvexError({
      code: "NOT_FOUND",
      message: "Resource not found",
      status: 404
    });
  }

  if (error.message.includes("Invalid")) {
    return new ConvexError({
      code: "BAD_REQUEST",
      message: "Invalid request parameters",
      status: 400
    });
  }

  // Generic server error (don't expose internal details)
  return new ConvexError({
    code: "INTERNAL_ERROR",
    message: "An internal error occurred",
    status: 500
  });
}

/**
 * Wraps a mutation function with structured error handling and logging.
 *
 * On error, logs the error details to the database and console, then throws a standardized ConvexError. Also logs execution time for successful runs.
 *
 * @returns The result of the wrapped mutation function.
 */
export function withErrorHandling<T extends unknown[], R>(
  fn: (ctx: MutationCtx, ...args: T) => Promise<R>
) {
  return async (ctx: MutationCtx, ...args: T): Promise<R> => {
    const functionName = fn.name;
    const startTime = Date.now();

    try {
      const result = await fn(ctx, ...args);

      // Log successful execution for monitoring
      console.log(`[${functionName}] Executed successfully in ${Date.now() - startTime}ms`);

      return result;
    } catch (error) {
      const userId = await getAuthUserId(ctx);
      const timestamp = Date.now();

      // Log the error
      await logError(ctx, {
        functionName,
        userId: userId ?? undefined, // Convert null to undefined
        sessionId: (() => {
          const argWithSessionId = args.find((arg: unknown): arg is { sessionId?: string } =>
            typeof arg === 'object' && arg !== null && 'sessionId' in arg
          );
          return argWithSessionId?.sessionId;
        })(),
        inputParams: args.length > 0 ? args[0] as Record<string, unknown> : {},
        error: error as Error,
        timestamp,
      });

      // Console log for development
      console.error(`[${functionName}] Error after ${timestamp - startTime}ms:`, {
        error: (error as Error).message,
        stack: (error as Error).stack,
        userId,
        args: JSON.stringify(args, null, 2),
      });

      // Throw appropriate ConvexError
      throw createConvexError(error as Error);
    }
  };
}

/**
 * Wraps a query function with standardized error handling and logging.
 *
 * If the wrapped function throws an error, logs error details to the console and rethrows a structured `ConvexError`.
 */
export function withQueryErrorHandling<T extends unknown[], R>(
  fn: (ctx: QueryCtx, ...args: T) => Promise<R>
) {
  return async (ctx: QueryCtx, ...args: T): Promise<R> => {
    const functionName = fn.name;
    const startTime = Date.now();

    try {
      const result = await fn(ctx, ...args);
      return result;
    } catch (error) {
      const userId = await getAuthUserId(ctx);
      const timestamp = Date.now();

      // Query context can't write to DB, so just log to console
      // await logError(ctx, {
      //   functionName,
      //   userId: userId || undefined,
      //   inputParams: args,
      //   error: error as Error,
      //   timestamp,
      // });

      console.error(`[${functionName}] Query error after ${timestamp - startTime}ms:`, {
        error: (error as Error).message,
        userId,
      });

      throw createConvexError(error as Error);
    }
  };
}

/**
 * Wraps an action function with standardized error handling and logging.
 *
 * On error, logs detailed information to the console and throws a structured ConvexError.
 *
 * @returns A new action function with integrated error handling.
 */
export function withActionErrorHandling<T extends unknown[], R>(
  fn: (ctx: ActionCtx, ...args: T) => Promise<R>
) {
  return async (ctx: ActionCtx, ...args: T): Promise<R> => {
    const functionName = fn.name;
    const startTime = Date.now();

    try {
      const result = await fn(ctx, ...args);

      console.log(`[${functionName}] Action executed successfully in ${Date.now() - startTime}ms`);

      return result;
    } catch (error) {
      const timestamp = Date.now();

      // Actions can't write to DB directly, so just log to console
      console.error(`[${functionName}] Action error after ${timestamp - startTime}ms:`, {
        error: (error as Error).message,
        stack: (error as Error).stack,
        args: JSON.stringify(args, null, 2),
      });

      throw createConvexError(error as Error);
    }
  };
}

