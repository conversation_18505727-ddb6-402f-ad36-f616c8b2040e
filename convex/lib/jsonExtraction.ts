/**
 * Robust JSON extraction utilities for handling LLM responses
 * that may contain explanatory text around JSON objects
 */

/**
 * Type-safe JSON extraction for UnixCoreObjective
 * Based on the actual structure used in clarificationAgent
 */
export interface UnixCoreObjective {
  objective: string;
  confidence: number;
  scopeCreepDetected: boolean;
  removedFunctions: string[];
  clarificationTurns: number;
  readyForPRD: boolean;
}

/**
 * Extracts and parses JSON from LLM response text that may contain additional text
 * @param responseText - The raw response text from the LLM
 * @param fallbackValue - Optional fallback value if JSON extraction fails
 * @returns Parsed JSON object
 * @throws Error if no valid JSON is found and no fallback is provided
 */
export function extractAndParseJSON<T = Record<string, unknown>>(
  responseText: string,
  fallbackValue?: T
): T {
  if (!responseText || typeof responseText !== 'string') {
    if (fallbackValue !== undefined) {
      return fallbackValue;
    }
    throw new Error("Response text is empty or invalid");
  }

  // Try multiple JSON extraction strategies
  const strategies = [
    // Strategy 1: Look for complete JSON objects (most common)
    () => {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      return jsonMatch ? jsonMatch[0] : null;
    },

    // Strategy 2: Look for JSON arrays
    () => {
      const jsonMatch = responseText.match(/\[[\s\S]*\]/);
      return jsonMatch ? jsonMatch[0] : null;
    },

    // Strategy 3: Look for JSON between code blocks
    () => {
      const codeBlockMatch = responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/i);
      return codeBlockMatch ? codeBlockMatch[1] : null;
    },

    // Strategy 4: Look for JSON between specific markers
    () => {
      const markerMatch = responseText.match(/(?:json|result|response):\s*(\{[\s\S]*?\})/i);
      return markerMatch ? markerMatch[1] : null;
    },

    // Strategy 5: Try the entire response as-is (fallback)
    () => responseText.trim()
  ];

  let lastError: Error | null = null;

  for (const strategy of strategies) {
    try {
      const extractedText = strategy();
      if (extractedText) {
        const parsed = JSON.parse(extractedText);
        return parsed as T;
      }
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      continue;
    }
  }

  // If all strategies failed, use fallback or throw error
  if (fallbackValue !== undefined) {
    return fallbackValue;
  }

  throw new Error(
    `Failed to extract valid JSON from response. Last error: ${lastError?.message || 'Unknown error'}`
  );
}

/**
 * Validates that a parsed object matches expected structure
 * @param obj - The parsed object to validate
 * @param requiredFields - Array of required field names
 * @returns boolean indicating if validation passed
 */
export function validateJSONStructure(
  obj: unknown,
  requiredFields: string[]
): boolean {
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  return requiredFields.every(field => field in obj);
}

/**
 * Enhanced JSON extraction with structure validation
 * @param responseText - The raw response text from the LLM
 * @param requiredFields - Array of required field names for validation
 * @param fallbackValue - Optional fallback value if extraction/validation fails
 * @returns Parsed and validated JSON object
 */
export function extractAndValidateJSON<T = Record<string, unknown>>(
  responseText: string,
  requiredFields: string[] = [],
  fallbackValue?: T
): T {
  try {
    const parsed = extractAndParseJSON<T>(responseText);

    if (requiredFields.length > 0 && !validateJSONStructure(parsed, requiredFields)) {
      throw new Error(`Parsed JSON missing required fields: ${requiredFields.join(', ')}`);
    }

    return parsed;
  } catch (error) {
    if (fallbackValue !== undefined) {
      return fallbackValue;
    }
    throw error;
  }
}

/**
 * Specialized extractor for UnixCoreObjective type
 * @param responseText - The raw response text from the LLM
 * @returns Parsed UnixCoreObjective object
 */
export function extractUnixCoreObjective(responseText: string): UnixCoreObjective {
  const requiredFields = ['objective', 'confidence', 'scopeCreepDetected', 'removedFunctions', 'clarificationTurns', 'readyForPRD'];

  return extractAndValidateJSON<UnixCoreObjective>(
    responseText,
    requiredFields,
    // Fallback object with default values
    {
      objective: 'Feature clarification needed',
      confidence: 0.5,
      scopeCreepDetected: false,
      removedFunctions: [],
      clarificationTurns: 1,
      readyForPRD: false
    }
  );
}
