import { ConvexError } from "convex/values";
import { MutationCtx, QueryCtx, ActionCtx } from "../_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { RateLimiter, MINUTE } from "@convex-dev/rate-limiter";
import { components } from "../_generated/api";

/**
 * Rate limiting configuration using official Convex rate limiter
 */
interface RateLimitConfig {
  rate: number;
  capacity: number;
  identifier?: string;
}

/**
 * Default rate limits by function type
 */
const DEFAULT_LIMITS = {
  mutation: { rate: 10, capacity: 20 }, // 10 per minute, burst capacity 20
  query: { rate: 50, capacity: 100 }, // 50 per minute, burst capacity 100
  action: { rate: 5, capacity: 10 }, // 5 per minute, burst capacity 10
  chatCompletion: { rate: 5, capacity: 10 }, // 5 per minute, burst capacity 10
} as const;

/**
 * Generates a unique rate limit identifier based on the authenticated user ID or IP address.
 *
 * If a user ID is available, returns an identifier in the form `user:<userId>`. If not, but an IP address is provided, returns `ip:<ipAddress>`. Throws an error if neither is available.
 *
 * @param ipAddress - Optional IP address to use if no user ID is present
 * @returns The rate limit identifier string
 */
async function createIdentifier(ctx: MutationCtx | QueryCtx | ActionCtx, ipAddress?: string): Promise<string> {
  const userId = await getAuthUserId(ctx);

  if (userId) {
    return `user:${userId}`;
  }

  if (ipAddress) {
    return `ip:${ipAddress}`;
  }

  throw new ConvexError("Cannot create rate limit identifier: no user or IP address");
}

/**
 * Official Convex rate limiter instance with predefined limits
 */
const rateLimiter = new RateLimiter(components.rateLimiter, {
  chatCompletion: { kind: "token bucket", rate: DEFAULT_LIMITS.chatCompletion.rate, period: MINUTE, capacity: DEFAULT_LIMITS.chatCompletion.capacity },
  mutation: { kind: "token bucket", rate: DEFAULT_LIMITS.mutation.rate, period: MINUTE, capacity: DEFAULT_LIMITS.mutation.capacity },
  query: { kind: "token bucket", rate: DEFAULT_LIMITS.query.rate, period: MINUTE, capacity: DEFAULT_LIMITS.query.capacity },
  action: { kind: "token bucket", rate: DEFAULT_LIMITS.action.rate, period: MINUTE, capacity: DEFAULT_LIMITS.action.capacity },
});

/**
 * Enforces and updates the rate limit for a specified identifier and function type.
 *
 * Throws a `ConvexError` with code `"RATE_LIMITED"` and status 429 if the rate limit is exceeded.
 */
export async function checkRateLimit(
  ctx: MutationCtx | ActionCtx,
  limitType: 'chatCompletion' | 'mutation' | 'query' | 'action',
  config: RateLimitConfig,
  ipAddress?: string
): Promise<void> {
  const identifier = config.identifier || await createIdentifier(ctx, ipAddress);
  const key = identifier;

  try {
    await rateLimiter.limit(ctx, limitType, { key });
  } catch {
    throw new ConvexError({
      code: "RATE_LIMITED",
      message: `Rate limit exceeded. Maximum ${config.rate} requests per minute.`,
      status: 429
    });
  }
}

/**
 * Rate limiting middleware for mutations
 */
export function withRateLimit<T extends unknown[], R>(
  fn: (ctx: MutationCtx, ...args: T) => Promise<R>,
  config?: Partial<RateLimitConfig>
) {
  return async (ctx: MutationCtx, ...args: T): Promise<R> => {
    // Skip rate limiting in test environment (convex-test doesn't enforce limits)
    const isTestEnvironment = process.env.NODE_ENV === 'test' ||
                             process.env.VITEST === 'true' ||
                             // Check if we're in convex-test environment
                             (ctx as unknown as { __convexTest?: boolean }).__convexTest === true;

    if (!isTestEnvironment) {
      const limitConfig = { ...DEFAULT_LIMITS.mutation, ...config };

      // Extract ipAddress from the first argument if it exists
      const firstArg = args[0] as unknown as { ipAddress?: string };
      const ipAddress = firstArg?.ipAddress;

      await checkRateLimit(ctx, 'mutation', limitConfig, ipAddress);
    }

    return fn(ctx, ...args);
  };
}

/**
 * Rate limiting middleware for actions (more restrictive)
 */

// Define a specific interface for action arguments that may include ipAddress
export interface ActionIpArg {
  ipAddress: string;
  [key: string]: unknown;
}

export function withActionRateLimit<T extends unknown[], R>(
  fn: (ctx: ActionCtx, ...args: T) => Promise<R>,
  config?: Partial<RateLimitConfig>,
  ipArgIndex?: number // Optionally specify which argument contains the IP
) {
  return async (ctx: ActionCtx, ...args: T): Promise<R> => {
    // Skip rate limiting in test environment (convex-test doesn't enforce limits)
    const isTestEnvironment = process.env.NODE_ENV === 'test' ||
                             process.env.VITEST === 'true' ||
                             // Check if we're in convex-test environment
                             (ctx as unknown as { __convexTest?: boolean }).__convexTest === true;

    if (!isTestEnvironment) {
      const limitConfig = { ...DEFAULT_LIMITS.action, ...config };

      // Extract IP from a specific argument if provided, else undefined
      let ipAddress: string | undefined = undefined;
      if (typeof ipArgIndex === 'number' && args[ipArgIndex] && typeof args[ipArgIndex] === 'object' && 'ipAddress' in args[ipArgIndex]) {
        const argWithIp = args[ipArgIndex] as unknown as ActionIpArg;
        ipAddress = argWithIp.ipAddress;
      }

      await checkRateLimit(ctx, 'action', limitConfig, ipAddress);
    }

    return fn(ctx, ...args);
  };
}

/**
 * Enforces the chat completion rate limit for a given identifier.
 *
 * Throws a `ConvexError` with code `"RATE_LIMITED"` and status 429 if the chat completion rate limit is exceeded.
 */
export async function checkChatRateLimit(
  ctx: MutationCtx,
  identifier: string
): Promise<void> {
  const key = `chat:${identifier}`;

  try {
    await rateLimiter.limit(ctx, "chatCompletion", { key });
  } catch {
    throw new ConvexError({
      code: "RATE_LIMITED",
      message: "Chat rate limit exceeded. Please wait before sending another message.",
      status: 429
    });
  }
}