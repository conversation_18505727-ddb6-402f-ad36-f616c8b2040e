import { v, ConvexError } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api } from "./_generated/api";

import { ensureUniqueSessionId } from "./lib/sessionUtils";
import { detectMode, isClarificationComplete } from "./lib/modeDetection";



/**
 * Create a new conversation
 */
export const createConversation = mutation({
  args: {
    sessionId: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);

    // Ensure unique session ID
    const sessionId = await ensureUniqueSessionId(ctx, args.sessionId);

    const conversationId = await ctx.db.insert("conversations", {
      userId: userId ?? undefined,
      sessionId,
      title: undefined,
      status: "active",
      currentMode: "clarification",
      clarificationComplete: false,
      prdGenerated: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      lastMessageAt: Date.now(),
      ipAddress: args.ipAddress,
      userAgent: args.userAgent,
    });

    return conversationId;
  },
});

/**
 * Add a message to a conversation
 */
export const addMessage = mutation({
  args: {
    conversationId: v.id("conversations"),
    role: v.union(v.literal("user"), v.literal("assistant"), v.literal("system")),
    content: v.string(),
    messageIndex: v.number(),
    aiProvider: v.optional(v.union(v.literal("anthropic"), v.literal("openai"))),
    model: v.optional(v.string()),
    promptTokens: v.optional(v.number()),
    completionTokens: v.optional(v.number()),
    totalTokens: v.optional(v.number()),
    temperature: v.optional(v.number()),
    maxTokens: v.optional(v.number()),
    generationTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Validate conversation exists
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new ConvexError("Conversation not found");
    }

    // Add the message
    const messageId = await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      role: args.role,
      content: args.content,
      messageIndex: args.messageIndex,
      aiProvider: args.aiProvider,
      model: args.model,
      promptTokens: args.promptTokens,
      completionTokens: args.completionTokens,
      totalTokens: args.totalTokens,
      temperature: args.temperature,
      maxTokens: args.maxTokens,
      generationTime: args.generationTime,
      createdAt: Date.now(),
    });

    // Update conversation timestamp
    await ctx.db.patch(args.conversationId, {
      updatedAt: Date.now(),
      lastMessageAt: Date.now(),
    });

    return messageId;
  },
});

/**
 * Get messages for a conversation
 */
export const getMessages = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);

    // Verify conversation belongs to user or is accessible
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new ConvexError("Conversation not found");
    }

    // For auth users, check ownership; for anonymous, allow access
    if (userId && 'userId' in conversation && conversation.userId && conversation.userId !== userId) {
      throw new ConvexError("Access denied");
    }

    return await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) => q.eq("conversationId", args.conversationId))
      .order("asc")
      .collect();
  },
});

/**
 * Update conversation mode based on message analysis
 */
export const updateConversationMode = mutation({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new Error("Conversation not found");
    }

    // Get all messages for mode detection
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) => q.eq("conversationId", args.conversationId))
      .order("asc")
      .collect();

    const messageData = messages.map(msg => ({
      content: msg.content,
      role: msg.role
    }));

    const newMode = detectMode(messageData);
    const clarificationComplete = isClarificationComplete(messageData);

    await ctx.db.patch(args.conversationId, {
      currentMode: newMode,
      clarificationComplete,
      updatedAt: Date.now(),
    });

    return { mode: newMode, clarificationComplete };
  },
});

/**
 * Chat completion action using external AI services
 */
export const chatCompletion = action({
  args: {
    conversationId: v.id("conversations"),
    messages: v.array(v.object({
      role: v.union(v.literal("user"), v.literal("assistant"), v.literal("system")),
      content: v.string(),
    })),
    provider: v.optional(v.union(v.literal("anthropic"), v.literal("openai"))),
    temperature: v.optional(v.number()),
    maxTokens: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { anthropic } = await import("@ai-sdk/anthropic");
    const { openai } = await import("@ai-sdk/openai");
    const { streamText } = await import("ai");

    // Determine AI provider
    const hasAnthropic = process.env.ANTHROPIC_API_KEY && process.env.ANTHROPIC_API_KEY !== 'your_anthropic_api_key_here';
    const hasOpenAI = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here';

    let aiProvider;
    let model;

    if (args.provider === "anthropic" && hasAnthropic) {
      aiProvider = "anthropic";
      model = anthropic('claude-3-5-sonnet-20241022');
    } else if (args.provider === "openai" && hasOpenAI) {
      aiProvider = "openai";
      model = openai('gpt-3.5-turbo');
    } else if (hasOpenAI) {
      aiProvider = "openai";
      model = openai('gpt-3.5-turbo');
    } else if (hasAnthropic) {
      aiProvider = "anthropic";
      model = anthropic('claude-3-5-sonnet-20241022');
    } else {
      throw new Error('No valid API key found');
    }

    // Get current conversation mode for system prompt
    const conversation = await ctx.runQuery(api.chat.getConversation, {
      conversationId: args.conversationId
    });

    if (!conversation) {
      throw new Error("Conversation not found");
    }

    // Import system prompt selector (we'll need to create this)
    const systemPrompt = await getSystemPromptForMode(conversation.currentMode);

    const startTime = Date.now();

    try {
      await streamText({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          ...args.messages
        ],
        temperature: args.temperature ?? 0.7,
        maxTokens: args.maxTokens ?? 4000,
      });

      const generationTime = Date.now() - startTime;

      // For now, we'll return the stream result
      // In a full implementation, we'd need to handle streaming differently
      return {
        success: true,
        provider: aiProvider,
        generationTime,
      };

    } catch (error) {
      console.error('AI completion error:', error);
      throw new Error('Failed to generate AI response');
    }
  },
});

/**
 * Get conversation details
 */
export const getConversation = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      return null;
    }

    // Check access permissions - only for conversations table
    if (userId && 'userId' in conversation && conversation.userId && conversation.userId !== userId) {
      return null;
    }

    return conversation;
  },
});

/**
 * Returns the system prompt string corresponding to the specified conversation mode.
 *
 * @param mode - The current mode of the conversation ("clarification", "generation", or "refinement")
 * @returns The system prompt to be used for the given mode
 */
async function getSystemPromptForMode(mode: 'clarification' | 'generation' | 'refinement'): Promise<string> {
  // This would import from our existing prompts library
  // For now, return a basic prompt
  const prompts = {
    clarification: "You are PRDGeneral, a focused PRD clarification engine. Ask pointed questions to clarify the product idea.",
    generation: "Generate a comprehensive PRD based on the clarified requirements.",
    refinement: "Help refine and improve the existing PRD based on user feedback."
  };

  return prompts[mode];
}