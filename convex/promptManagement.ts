/**
 * Enhanced system prompt management with Convex-based context switching
 * This provides dynamic prompt switching with real-time state synchronization
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { api } from "./_generated/api";
import {
  SYSTEM_PROMPT_SELECTOR
} from "./lib/prompts";

/**
 * Interface for conversation analysis results
 */
interface ConversationAnalysis {
  messageCount: number;
  userMessageCount: number;
  assistantMessageCount: number;
  currentMode: string;
  clarificationComplete: boolean;
  prdGenerated: boolean;
  hasBackAndForth: boolean;
  hasDetailedResponses: boolean;
  hasSpecificQuestions: boolean;
  mentionsPRD: boolean;
  mentionsGeneration: boolean;
}

/**
 * Interface for conversation context
 */
interface ConversationContext {
  currentMode: string;
  clarificationComplete: boolean;
  prdGenerated: boolean;
}

/**
 * Interface for mode suggestions
 */
interface ModeSuggestion {
  mode: 'clarification' | 'generation' | 'refinement';
  reason: string;
  confidence: number;
}

/**
 * Enhanced prompt context with conversation history and mode analysis
 */
interface PromptContext {
  currentMode: 'clarification' | 'generation' | 'refinement';
  previousMode?: 'clarification' | 'generation' | 'refinement';
  conversationLength: number;
  clarificationComplete: boolean;
  prdGenerated: boolean;
  userPreferences?: {
    detailLevel?: 'concise' | 'standard' | 'detailed';
    industry?: string;
    experience?: 'beginner' | 'intermediate' | 'expert';
  };
}

/**
 * Get the appropriate system prompt with enhanced context
 */
export const getEnhancedSystemPrompt = query({
  args: {
    conversationId: v.id("conversations"),
    requestedMode: v.optional(v.union(
      v.literal("clarification"),
      v.literal("generation"),
      v.literal("refinement")
    )),
    includeTransitionContext: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{
    prompt: string;
    mode: 'clarification' | 'generation' | 'refinement';
    context: PromptContext;
    hasTransition: boolean;
    promptLength: number;
  }> => {
    // Get conversation context
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new Error("Conversation not found");
    }

    // Get message history for context
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .order("asc")
      .collect();

    // Determine the target mode
    const targetMode = args.requestedMode || conversation.currentMode;
    const previousMode = conversation.currentMode !== targetMode ?
      conversation.currentMode : undefined;

    // Build prompt context
    const promptContext: PromptContext = {
      currentMode: targetMode,
      previousMode,
      conversationLength: messages.length,
      clarificationComplete: conversation.clarificationComplete,
      prdGenerated: conversation.prdGenerated,
    };

    // Get base prompt
    const basePrompt = SYSTEM_PROMPT_SELECTOR(targetMode);

    // Add mode transition context if requested
    let transitionContext = '';
    if (args.includeTransitionContext && previousMode && previousMode !== targetMode) {
      transitionContext = createModeTransitionContext(targetMode, previousMode);
    }

    // Add conversation-specific context
    const conversationContext = createConversationContext(promptContext);

    // Combine all prompt components
    const enhancedPrompt = [
      basePrompt,
      transitionContext,
      conversationContext
    ].filter(Boolean).join('\n\n');

    return {
      prompt: enhancedPrompt,
      mode: targetMode,
      context: promptContext,
      hasTransition: !!transitionContext,
      promptLength: enhancedPrompt.length
    };
  },
});

/**
 * Update conversation mode with enhanced context switching
 */
export const switchConversationMode = mutation({
  args: {
    conversationId: v.id("conversations"),
    newMode: v.union(
      v.literal("clarification"),
      v.literal("generation"),
      v.literal("refinement")
    ),
    reason: v.optional(v.string()),
    userTriggered: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    previousMode: 'clarification' | 'generation' | 'refinement';
    newMode: 'clarification' | 'generation' | 'refinement';
    prompt: string;
    context: PromptContext;
  }> => {
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new Error("Conversation not found");
    }

    const previousMode = conversation.currentMode;
    const now = Date.now();

    // Update conversation mode
    await ctx.db.patch(args.conversationId, {
      currentMode: args.newMode,
      updatedAt: now,
    });

    // Log the mode transition for analytics
    await ctx.db.insert("modeTransitionLogs", {
      conversationId: args.conversationId,
      fromMode: previousMode,
      toMode: args.newMode,
      reason: args.reason || 'automatic_detection',
      userTriggered: args.userTriggered || false,
      timestamp: now,
    });

    // Get the new enhanced prompt
    const promptResult = await ctx.runQuery(api.promptManagement.getEnhancedSystemPrompt, {
      conversationId: args.conversationId,
      requestedMode: args.newMode,
      includeTransitionContext: true,
    });

    return {
      success: true,
      previousMode,
      newMode: args.newMode,
      prompt: promptResult.prompt,
      context: promptResult.context,
    };
  },
});

/**
 * Get mode transition history for a conversation
 */
export const getModeTransitionHistory = query({
  args: {
    conversationId: v.id("conversations"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<Array<{
    id: string;
    fromMode: 'clarification' | 'generation' | 'refinement';
    toMode: 'clarification' | 'generation' | 'refinement';
    reason: string;
    userTriggered: boolean;
    timestamp: number;
    timeAgo: number;
  }>> => {
    const transitions = await ctx.db
      .query("modeTransitionLogs")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .order("desc")
      .take(args.limit || 20);

    return transitions.map(transition => ({
      id: transition._id,
      fromMode: transition.fromMode,
      toMode: transition.toMode,
      reason: transition.reason,
      userTriggered: transition.userTriggered,
      timestamp: transition.timestamp,
      timeAgo: Date.now() - transition.timestamp,
    }));
  },
});

/**
 * Analyze conversation for optimal mode suggestions
 */
export const analyzeConversationForModeSwitch = query({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args): Promise<{
    analysis: ConversationAnalysis;
    suggestions: ModeSuggestion[];
    confidence: number;
  }> => {
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation) {
      throw new Error("Conversation not found");
    }

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .order("asc")
      .collect();

    const userMessages = messages.filter(msg => msg.role === 'user');
    const assistantMessages = messages.filter(msg => msg.role === 'assistant');

    // Analyze conversation characteristics
    const analysis = {
      messageCount: messages.length,
      userMessageCount: userMessages.length,
      assistantMessageCount: assistantMessages.length,
      currentMode: conversation.currentMode,
      clarificationComplete: conversation.clarificationComplete,
      prdGenerated: conversation.prdGenerated,

      // Conversation depth indicators
      hasBackAndForth: userMessages.length >= 3 && assistantMessages.length >= 3,
      hasDetailedResponses: assistantMessages.some(msg => msg.content.length > 500),
      hasSpecificQuestions: userMessages.some(msg =>
        msg.content.includes('?') && msg.content.length > 20
      ),

      // Content analysis
      mentionsPRD: messages.some(msg =>
        msg.content.toLowerCase().includes('prd') ||
        msg.content.toLowerCase().includes('product requirements')
      ),
      mentionsGeneration: messages.some(msg =>
        /\b(generate|create|write|build)\b/i.test(msg.content)
      ),
    };

    // Generate mode suggestions
    const suggestions = generateModeSuggestions(analysis, conversation);

    return {
      analysis,
      suggestions,
      confidence: calculateSuggestionConfidence(analysis),
    };
  },
});

/**
 * Returns a descriptive context string explaining the transition between two conversation modes.
 *
 * If the transition between the specified previous and new modes is recognized, a detailed explanation is provided; otherwise, an empty string is returned.
 *
 * @param newMode - The target conversation mode after the transition
 * @param previousMode - The conversation mode prior to the transition
 * @returns A string describing the mode transition, or an empty string if the transition is not recognized
 */
function createModeTransitionContext(
  newMode: string,
  previousMode: string
): string {
  const transitions: Record<string, string> = {
    'clarification->generation': `
**MODE TRANSITION**: The user has completed the clarification process and is ready for PRD generation.
All 4 clarification steps appear to be addressed. Use the clarified information to create a focused PRD.`,

    'clarification->refinement': `
**MODE TRANSITION**: The user wants to refine or modify content.
Help them improve their existing work with specific, targeted changes.`,

    'generation->refinement': `
**MODE TRANSITION**: The user wants to refine the generated PRD.
Focus on specific improvements while maintaining the PRD structure and single-purpose philosophy.`,

    'generation->clarification': `
**MODE TRANSITION**: The user needs more clarification before proceeding.
Return to the rigorous 4-step clarification process to ensure clarity.`,

    'refinement->generation': `
**MODE TRANSITION**: The user wants to generate a new PRD after refinements.
Incorporate the refined requirements into a fresh PRD generation.`,

    'refinement->clarification': `
**MODE TRANSITION**: The user needs to clarify requirements before proceeding.
Use the 4-step process to ensure all requirements are crystal clear.`
  };

  const transitionKey = `${previousMode}->${newMode}`;
  return transitions[transitionKey] || '';
}

/**
 * Generates a context string summarizing the conversation's current progress, clarification status, and PRD generation state.
 *
 * @param context - Contains metadata about the conversation's mode, length, clarification, and PRD status.
 * @returns A formatted string describing the conversation context for prompt enhancement.
 */
function createConversationContext(context: PromptContext): string {
  const contextParts = [];

  // Add conversation progress context
  if (context.conversationLength > 0) {
    contextParts.push(`**CONVERSATION CONTEXT**: This is message ${context.conversationLength + 1} in an ongoing conversation.`);
  }

  // Add clarification progress
  if (context.currentMode === 'clarification' && !context.clarificationComplete) {
    contextParts.push(`**CLARIFICATION STATUS**: Continue working through the 4-step clarification process. Don't generate PRD until all steps are complete.`);
  }

  // Add PRD status
  if (context.prdGenerated && context.currentMode === 'refinement') {
    contextParts.push(`**PRD STATUS**: A PRD has been generated. Focus on specific improvements based on user feedback.`);
  }

  return contextParts.join('\n');
}

/**
 * Helper function to generate mode suggestions
 */
function generateModeSuggestions(analysis: ConversationAnalysis, conversation: ConversationContext): ModeSuggestion[] {
  const suggestions: ModeSuggestion[] = [];

  // Suggest generation if clarification seems complete
  if (
    conversation.currentMode === 'clarification' &&
    analysis.hasBackAndForth &&
    analysis.messageCount >= 8 &&
    !conversation.prdGenerated
  ) {
    suggestions.push({
      mode: 'generation',
      reason: 'Conversation shows sufficient depth for PRD generation',
      confidence: 0.7,
    });
  }

  // Suggest refinement if PRD exists
  if (
    conversation.prdGenerated &&
    conversation.currentMode !== 'refinement' &&
    analysis.mentionsGeneration
  ) {
    suggestions.push({
      mode: 'refinement',
      reason: 'PRD exists and user mentions modifications',
      confidence: 0.8,
    });
  }

  return suggestions;
}

/**
 * Calculates a confidence score for mode suggestions based on conversation analysis metrics.
 *
 * The score increases from a base value according to the presence of back-and-forth exchanges, detailed responses, specific questions, and sufficient message count, and is capped at 1.0.
 *
 * @param analysis - The analysis results of the conversation used to determine confidence factors.
 * @returns The computed confidence score between 0.5 and 1.0.
 */
function calculateSuggestionConfidence(analysis: ConversationAnalysis): number {
  let confidence = 0.5; // Base confidence

  if (analysis.hasBackAndForth) confidence += 0.2;
  if (analysis.hasDetailedResponses) confidence += 0.1;
  if (analysis.hasSpecificQuestions) confidence += 0.1;
  if (analysis.messageCount >= 10) confidence += 0.1;

  return Math.min(confidence, 1.0);
}
